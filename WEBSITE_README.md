# 🎧 Kokoro TTS Web Application

موقع ويب كامل لتحويل النص إلى صوت باستخدام نموذج Kokoro مع واجهة جميلة و API متوافق مع n8n.

## ✨ المميزات

### 🌐 واجهة ويب جميلة
- تصميم حديث ومتجاوب
- دعم الأجهزة المحمولة
- واجهة سهلة الاستخدام
- إحصائيات مباشرة

### 🎤 أصوات عالية الجودة
- **Nicole** 🇺🇸 🚺 🎧 - الأفضل للقصص
- **Heart** 🇺🇸 🚺 ❤️ - صوت دافئ
- **Bella** 🇺🇸 🚺 🔥 - صوت مميز
- **Michael** 🇺🇸 🚹 - صوت ذكوري
- **Emma** 🇬🇧 🚺 - بريطاني أنيق
- **George** 🇬🇧 🚹 - بريطاني كلاسيكي

### 🔧 API متقدم
- متوافق مع Gradio
- يعمل مع n8n
- إحصائيات مفصلة
- معالجة أخطاء ذكية

## 🚀 التثبيت والتشغيل

### 1. التثبيت السريع
```bash
# شغّل ملف التثبيت
install_kokoro.bat

# ثبّت espeak-ng من الرابط المعطى
# https://github.com/espeak-ng/espeak-ng/releases
```

### 2. تشغيل الموقع
```bash
# شغّل الموقع
start_kokoro_server.bat

# سيفتح المتصفح تلقائياً على:
# http://localhost:5000
```

## 📡 API Endpoints

### 🎯 للاستخدام مع n8n
```javascript
// POST /api/predict
{
  "data": ["النص هنا", "af_nicole", 0.9]
}

// Response
{
  "data": [{"url": "http://localhost:5000/static/audio.wav"}],
  "success": true,
  "duration": 5.2,
  "voice": "af_nicole"
}
```

### 🔧 API مباشر
```javascript
// POST /tts_url
{
  "text": "النص هنا",
  "voice": "af_nicole",
  "speed": 0.9
}

// Response
{
  "success": true,
  "audioUrl": "http://localhost:5000/static/audio.wav",
  "voice": "af_nicole",
  "duration": 5.2
}
```

### 📊 إحصائيات
```javascript
// GET /stats
{
  "total_requests": 150,
  "total_characters": 25000,
  "total_audio_duration": 300.5,
  "voice_usage": {
    "af_nicole": 80,
    "af_heart": 45,
    "af_bella": 25
  },
  "uptime_hours": 24.5
}
```

## 🎮 استخدام الواجهة

### 1. إدخال النص
- اكتب أو الصق النص في المربع
- يدعم حتى 1000 حرف
- مناسب للقصص الطويلة

### 2. اختيار الصوت
- **Nicole**: الأفضل لقصص الأطفال 🎧
- **Heart**: صوت دافئ وهادئ ❤️
- **Bella**: صوت مميز وواضح 🔥

### 3. ضبط السرعة
- 0.5 - 2.0
- 0.9 مثالي للقصص
- 1.0 للقراءة العادية

### 4. توليد الصوت
- اضغط "Generate Speech"
- انتظر التحميل
- استمع أو حمّل الملف

## 🔗 التكامل مع n8n

### إعداد HTTP Request في n8n:

**Method:** POST  
**URL:** `http://localhost:5000/api/predict`  
**Headers:**
```json
{
  "Content-Type": "application/json"
}
```
**Body:**
```json
{
  "data": ["{{ $json.text }}", "af_nicole", 0.9]
}
```

### معالجة النتيجة:
```javascript
// في Function node
const response = $input.item.json;
const audioUrl = response.data[0].url;

return {
  json: {
    audioUrl: audioUrl,
    voice: "af_nicole",
    success: response.success
  }
};
```

## 📁 هيكل المشروع

```
kokoro-website/
├── kokoro_server.py          # الخادم الرئيسي
├── templates/
│   └── index.html           # واجهة الويب
├── static/                  # ملفات الصوت المولدة
├── install_kokoro.bat       # ملف التثبيت
├── start_kokoro_server.bat  # ملف التشغيل
├── requirements.txt         # المتطلبات
└── README.md               # هذا الملف
```

## 🎯 أمثلة للاستخدام

### قصة قبل النوم
```
"Once upon a time, in a magical forest far away, there lived a little rabbit named Luna. Every night before bed, Luna would look up at the twinkling stars and make a wish for sweet dreams and wonderful adventures."
```

### نص تعليمي
```
"Welcome to our learning session. Today we will explore the fascinating world of artificial intelligence and how it can help us create amazing applications."
```

### إعلان
```
"Attention everyone! We are excited to announce our new product launch. Join us for an amazing journey into the future of technology."
```

## 🛠️ استكشاف الأخطاء

### المشكلة: لا يعمل الموقع
**الحل:**
1. تأكد من تشغيل `install_kokoro.bat`
2. تأكد من تثبيت espeak-ng
3. تحقق من Python 3.8+

### المشكلة: لا يولد صوت
**الحل:**
1. تحقق من النص (لا يكون فارغ)
2. جرب صوت آخر
3. تحقق من logs في Terminal

### المشكلة: بطء في التوليد
**الحل:**
1. قلل طول النص
2. استخدم GPU إذا متوفر
3. أعد تشغيل الخادم

## 🔧 تخصيص الموقع

### إضافة أصوات جديدة:
```python
# في kokoro_server.py
VOICES['new_voice'] = {
    'name': '🇺🇸 🚺 New Voice',
    'lang': 'en-US',
    'gender': 'female',
    'quality': 'A',
    'recommended': True
}
```

### تغيير المنفذ:
```python
# في نهاية kokoro_server.py
app.run(host='0.0.0.0', port=8000, debug=False)
```

### تخصيص الواجهة:
عدّل ملف `templates/index.html` لتغيير التصميم والألوان.

## 📈 الإحصائيات

الموقع يتتبع:
- عدد الطلبات الكلي
- عدد الأحرف المعالجة
- مدة الصوت المولد
- استخدام كل صوت
- وقت تشغيل الخادم

## 🌟 نصائح للحصول على أفضل نتيجة

1. **استخدم جمل واضحة** مع علامات ترقيم
2. **اختر الصوت المناسب** للمحتوى
3. **اضبط السرعة** حسب الغرض
4. **قسّم النصوص الطويلة** لأجزاء أصغر
5. **استخدم Nicole للقصص** 🎧

## 🎉 الآن موقعك جاهز!

شغّل `start_kokoro_server.bat` واستمتع بموقع TTS احترافي مجاني 100%! 🚀
