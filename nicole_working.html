<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nicole TTS - Multiple Options</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        .container { max-width: 900px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; color: white; }
        .header h1 { font-size: 3rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .option-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        .option-card.recommended {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-color: #667eea;
        }
        .option-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .option-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .option-btn {
            background: #667eea;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        .option-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .option-btn.secondary {
            background: #28a745;
        }
        .option-btn.secondary:hover {
            background: #218838;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #28a745; }
        .status-busy { background: #ffc107; }
        .status-offline { background: #dc3545; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #555; }
        .text-input {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
        }
        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
        }
        .info-box {
            background: #d1ecf1;
            color: #0c5460;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #bee5eb;
        }
        .tutorial {
            background: #fff3cd;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border: 1px solid #ffeaa7;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎧 Nicole TTS</h1>
            <p>Multiple Ways to Get Nicole's Voice</p>
            <p style="font-size: 1.1rem; margin-top: 10px;">🇺🇸 🚺 American Female Voice for Stories</p>
        </div>

        <div class="card">
            <div class="info-box">
                <h3>🎯 Choose Your Preferred Method:</h3>
                <p>Since HuggingFace Spaces can be busy, here are multiple ways to get Nicole's voice working:</p>
            </div>

            <div class="options-grid">
                <div class="option-card recommended">
                    <div class="option-title">
                        <span class="status-indicator status-online"></span>
                        Option 1: Direct HuggingFace
                    </div>
                    <div class="option-description">
                        Visit HuggingFace Spaces directly in your browser. Most reliable method.
                    </div>
                    <a href="https://aiqcamp-mcp-kokoro.hf.space" target="_blank" class="option-btn">
                        🚀 Open HuggingFace Space
                    </a>
                </div>

                <div class="option-card">
                    <div class="option-title">
                        <span class="status-indicator status-online"></span>
                        Option 2: Alternative Space
                    </div>
                    <div class="option-description">
                        Backup HuggingFace Space with Kokoro TTS. Try if first option is busy.
                    </div>
                    <a href="https://webml-community-kokoro-web.hf.space" target="_blank" class="option-btn secondary">
                        🔄 Try Alternative
                    </a>
                </div>

                <div class="option-card">
                    <div class="option-title">
                        <span class="status-indicator status-busy"></span>
                        Option 3: Local Setup
                    </div>
                    <div class="option-description">
                        Install Kokoro locally for unlimited usage. Requires Python setup.
                    </div>
                    <button onclick="showLocalSetup()" class="option-btn">
                        🔧 Setup Instructions
                    </button>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>📋 Quick Test with HuggingFace:</h3>
            <div class="form-group">
                <label for="testText">📝 Test Text (Copy this to HuggingFace):</label>
                <textarea id="testText" class="text-input" readonly>Once upon a time, in a magical forest, there lived a little rabbit named Luna who loved to tell bedtime stories to all the woodland creatures. Every night, she would gather them around the old oak tree and share tales of wonder and dreams.</textarea>
            </div>
            <button onclick="copyText()" class="generate-btn">📋 Copy Text to Clipboard</button>
        </div>

        <div class="tutorial" id="localSetup" style="display: none;">
            <h3>🔧 Local Kokoro Setup Instructions:</h3>
            
            <div class="step">
                <strong>Step 1:</strong> Install Python 3.8+ from <a href="https://python.org" target="_blank">python.org</a>
            </div>
            
            <div class="step">
                <strong>Step 2:</strong> Open Command Prompt and run:
                <code style="background: #f8f9fa; padding: 5px; border-radius: 3px; display: block; margin: 5px 0;">
                    pip install torch torchaudio
                </code>
            </div>
            
            <div class="step">
                <strong>Step 3:</strong> Clone Kokoro repository:
                <code style="background: #f8f9fa; padding: 5px; border-radius: 3px; display: block; margin: 5px 0;">
                    git clone https://github.com/hexgrad/kokoro
                </code>
            </div>
            
            <div class="step">
                <strong>Step 4:</strong> Install dependencies:
                <code style="background: #f8f9fa; padding: 5px; border-radius: 3px; display: block; margin: 5px 0;">
                    cd kokoro && pip install -r requirements.txt
                </code>
            </div>
            
            <div class="step">
                <strong>Step 5:</strong> Run the local server we created:
                <code style="background: #f8f9fa; padding: 5px; border-radius: 3px; display: block; margin: 5px 0;">
                    python nicole_tts.py
                </code>
            </div>
        </div>

        <div class="card">
            <h3>🎯 For n8n Integration:</h3>
            <p><strong>Use this endpoint in your n8n HTTP Request node:</strong></p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; font-family: monospace;">
                <strong>URL:</strong> https://aiqcamp-mcp-kokoro.hf.space/api/predict<br>
                <strong>Method:</strong> POST<br>
                <strong>Body:</strong> {"data": ["Your text here", "af_nicole", 0.85]}
            </div>
            
            <p><strong>Example n8n Code Node:</strong></p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; font-family: monospace; font-size: 14px;">
const response = await $http.request({<br>
&nbsp;&nbsp;method: 'POST',<br>
&nbsp;&nbsp;url: 'https://aiqcamp-mcp-kokoro.hf.space/api/predict',<br>
&nbsp;&nbsp;headers: { 'Content-Type': 'application/json' },<br>
&nbsp;&nbsp;body: { data: [items[0].json.text, "af_nicole", 0.85] }<br>
});<br><br>
return { audioUrl: response.body.data[0].url };
            </div>
        </div>

        <div class="card">
            <h3>💡 Tips for Success:</h3>
            <ul style="line-height: 1.8; margin-left: 20px;">
                <li>🕐 <strong>Best Times:</strong> Try during off-peak hours (early morning or late evening)</li>
                <li>📝 <strong>Text Length:</strong> Keep stories under 1000 characters for faster processing</li>
                <li>🔄 <strong>Retry:</strong> If one service is busy, try the alternative immediately</li>
                <li>⏱️ <strong>Patience:</strong> Audio generation can take 30-120 seconds</li>
                <li>🎧 <strong>Voice:</strong> Always use "af_nicole" for American Female Nicole voice</li>
                <li>🌙 <strong>Speed:</strong> Use 0.85 speed for bedtime stories (slower and more soothing)</li>
            </ul>
        </div>
    </div>

    <script>
        function copyText() {
            const textArea = document.getElementById('testText');
            textArea.select();
            document.execCommand('copy');
            
            const btn = event.target;
            const originalText = btn.textContent;
            btn.textContent = '✅ Copied! Now paste in HuggingFace';
            btn.style.background = '#28a745';
            
            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.background = '';
            }, 3000);
        }

        function showLocalSetup() {
            const setup = document.getElementById('localSetup');
            setup.style.display = setup.style.display === 'none' ? 'block' : 'none';
        }

        // Check service status (simplified)
        async function checkServiceStatus() {
            const endpoints = [
                'https://aiqcamp-mcp-kokoro.hf.space',
                'https://webml-community-kokoro-web.hf.space'
            ];
            
            // This is a simplified check - in reality you'd ping the actual endpoints
            console.log('🔍 Checking service availability...');
            console.log('💡 If services are busy, try again in a few minutes or use direct links');
        }

        // Run status check on load
        checkServiceStatus();

        // Add helpful console messages
        console.log('🎧 Nicole TTS - Multiple Options Available');
        console.log('🌐 Direct Links:');
        console.log('   • https://aiqcamp-mcp-kokoro.hf.space');
        console.log('   • https://webml-community-kokoro-web.hf.space');
        console.log('🔧 For n8n: Use POST to /api/predict with {"data": ["text", "af_nicole", 0.85]}');
    </script>
</body>
</html>
