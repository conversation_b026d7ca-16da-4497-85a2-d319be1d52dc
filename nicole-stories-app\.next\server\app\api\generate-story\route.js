"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-story/route";
exports.ids = ["app/api/generate-story/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-story%2Froute&page=%2Fapi%2Fgenerate-story%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-story%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-story%2Froute&page=%2Fapi%2Fgenerate-story%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-story%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Ahmed_Downloads_kokoro_main_kokoro_main_nicole_stories_app_app_api_generate_story_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/generate-story/route.ts */ \"(rsc)/./app/api/generate-story/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-story/route\",\n        pathname: \"/api/generate-story\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-story/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\api\\\\generate-story\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Ahmed_Downloads_kokoro_main_kokoro_main_nicole_stories_app_app_api_generate_story_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-story/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-story%2Froute&page=%2Fapi%2Fgenerate-story%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-story%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/generate-story/route.ts":
/*!*****************************************!*\
  !*** ./app/api/generate-story/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\nasync function POST(request) {\n    try {\n        const { prompt } = await request.json();\n        if (!prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Prompt is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Enhanced prompt for bedtime stories\n        const enhancedPrompt = `Create a beautiful, soothing bedtime story suitable for children based on this idea: \"${prompt}\"\n\nRequirements:\n- Write in a calm, gentle tone perfect for bedtime\n- Include magical or whimsical elements\n- Keep it appropriate for all ages\n- Make it 200-400 words long\n- End with a peaceful, sleepy conclusion\n- Use descriptive, dreamy language\n- Include a gentle moral or lesson\n\nThe story should be narrated as if Nicole is telling it directly to the listener, starting with \"Hello, my name is Nicole. Tonight I'll tell you a wonderful story about...\"`;\n        const completion = await openai.chat.completions.create({\n            model: \"gpt-3.5-turbo\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"You are Nicole, a gentle storyteller who creates beautiful bedtime stories for children. Your voice is warm, soothing, and magical. You always create stories that help children drift off to peaceful sleep.\"\n                },\n                {\n                    role: \"user\",\n                    content: enhancedPrompt\n                }\n            ],\n            max_tokens: 800,\n            temperature: 0.8\n        });\n        const story = completion.choices[0]?.message?.content;\n        if (!story) {\n            throw new Error(\"No story generated\");\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            story\n        });\n    } catch (error) {\n        console.error(\"Error generating story:\", error);\n        // Fallback story if API fails\n        const fallbackStory = `Hello, my name is Nicole. Tonight I'll tell you a wonderful story about a little star named Luna.\n\nHigh up in the velvet sky, there lived a tiny star named Luna who was different from all the others. While the other stars shone brightly all night long, Luna's light would gently pulse like a soft heartbeat, creating the most beautiful, calming glow.\n\nOne evening, Luna noticed a little child on Earth who couldn't fall asleep. The child was tossing and turning, worried about the day ahead. Luna's kind heart filled with compassion, and she decided to help.\n\nSlowly, Luna began to pulse her gentle light in a rhythm as soft as a lullaby. The warm, golden glow drifted down like stardust, wrapping around the child's room like a cozy blanket. As Luna's light danced on the walls, it painted pictures of peaceful meadows, friendly animals, and magical gardens.\n\nThe child's eyes grew heavy as Luna's soothing light continued its gentle dance. Soon, the most wonderful dreams filled the child's mind - dreams of flying with friendly dragons, playing in fields of cotton candy clouds, and discovering treasure chests full of laughter and joy.\n\nFrom that night on, whenever children had trouble sleeping, Luna would share her special gift, helping them drift into the most peaceful, magical dreams. And now, just like that little child, it's time for you to close your eyes and let Luna's gentle light guide you to your own wonderful dreams.\n\nGood night, sweet dreams, and remember - there's always a friendly star watching over you.`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            story: fallbackStory\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: \"Story generation API is running\",\n        endpoints: {\n            \"POST /api/generate-story\": \"Generate a bedtime story\",\n            \"POST /api/text-to-speech\": \"Convert text to Nicole's voice\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/generate-story/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/event-target-shim","vendor-chunks/formdata-node","vendor-chunks/abort-controller","vendor-chunks/openai","vendor-chunks/form-data-encoder","vendor-chunks/whatwg-url","vendor-chunks/agentkeepalive","vendor-chunks/tr46","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/webidl-conversions","vendor-chunks/ms","vendor-chunks/humanize-ms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-story%2Froute&page=%2Fapi%2Fgenerate-story%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-story%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();