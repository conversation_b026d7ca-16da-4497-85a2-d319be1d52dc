"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-story/route";
exports.ids = ["app/api/generate-story/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-story%2Froute&page=%2Fapi%2Fgenerate-story%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-story%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-story%2Froute&page=%2Fapi%2Fgenerate-story%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-story%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Ahmed_Downloads_kokoro_main_kokoro_main_nicole_stories_app_app_api_generate_story_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/generate-story/route.ts */ \"(rsc)/./app/api/generate-story/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-story/route\",\n        pathname: \"/api/generate-story\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-story/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\api\\\\generate-story\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Ahmed_Downloads_kokoro_main_kokoro_main_nicole_stories_app_app_api_generate_story_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-story/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-story%2Froute&page=%2Fapi%2Fgenerate-story%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-story%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/generate-story/route.ts":
/*!*****************************************!*\
  !*** ./app/api/generate-story/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n    apiKey: \"\"\n});\nasync function POST(request) {\n    try {\n        const { prompt } = await request.json();\n        if (!prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Prompt is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Enhanced prompt for bedtime stories\n        const enhancedPrompt = `Create a beautiful, soothing bedtime story suitable for children based on this idea: \"${prompt}\"\n\nRequirements:\n- Write in a calm, gentle tone perfect for bedtime\n- Include magical or whimsical elements\n- Keep it appropriate for all ages\n- Make it 200-400 words long\n- End with a peaceful, sleepy conclusion\n- Use descriptive, dreamy language\n- Include a gentle moral or lesson\n\nThe story should be narrated as if Nicole is telling it directly to the listener, starting with \"Hello, my name is Nicole. Tonight I'll tell you a wonderful story about...\"`;\n        const completion = await openai.chat.completions.create({\n            model: \"gpt-3.5-turbo\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"You are Nicole, a gentle storyteller who creates beautiful bedtime stories for children. Your voice is warm, soothing, and magical. You always create stories that help children drift off to peaceful sleep.\"\n                },\n                {\n                    role: \"user\",\n                    content: enhancedPrompt\n                }\n            ],\n            max_tokens: 800,\n            temperature: 0.8\n        });\n        const story = completion.choices[0]?.message?.content;\n        if (!story) {\n            throw new Error(\"No story generated\");\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            story\n        });\n    } catch (error) {\n        console.error(\"Error generating story:\", error);\n        // Fallback story if API fails\n        const fallbackStory = `Hello, my name is Nicole. Tonight I'll tell you a wonderful story about a little star named Luna.\n\nHigh up in the velvet sky, there lived a tiny star named Luna who was different from all the others. While the other stars shone brightly all night long, Luna's light would gently pulse like a soft heartbeat, creating the most beautiful, calming glow.\n\nOne evening, Luna noticed a little child on Earth who couldn't fall asleep. The child was tossing and turning, worried about the day ahead. Luna's kind heart filled with compassion, and she decided to help.\n\nSlowly, Luna began to pulse her gentle light in a rhythm as soft as a lullaby. The warm, golden glow drifted down like stardust, wrapping around the child's room like a cozy blanket. As Luna's light danced on the walls, it painted pictures of peaceful meadows, friendly animals, and magical gardens.\n\nThe child's eyes grew heavy as Luna's soothing light continued its gentle dance. Soon, the most wonderful dreams filled the child's mind - dreams of flying with friendly dragons, playing in fields of cotton candy clouds, and discovering treasure chests full of laughter and joy.\n\nFrom that night on, whenever children had trouble sleeping, Luna would share her special gift, helping them drift into the most peaceful, magical dreams. And now, just like that little child, it's time for you to close your eyes and let Luna's gentle light guide you to your own wonderful dreams.\n\nGood night, sweet dreams, and remember - there's always a friendly star watching over you.`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            story: fallbackStory\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: \"Story generation API is running\",\n        endpoints: {\n            \"POST /api/generate-story\": \"Generate a bedtime story\",\n            \"POST /api/text-to-speech\": \"Convert text to Nicole's voice\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/generate-story/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/event-target-shim","vendor-chunks/formdata-node","vendor-chunks/abort-controller","vendor-chunks/openai","vendor-chunks/form-data-encoder","vendor-chunks/whatwg-url","vendor-chunks/agentkeepalive","vendor-chunks/tr46","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/webidl-conversions","vendor-chunks/ms","vendor-chunks/humanize-ms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-story%2Froute&page=%2Fapi%2Fgenerate-story%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-story%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();