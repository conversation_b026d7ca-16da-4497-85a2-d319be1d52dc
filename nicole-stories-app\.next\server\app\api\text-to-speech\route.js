"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/text-to-speech/route";
exports.ids = ["app/api/text-to-speech/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftext-to-speech%2Froute&page=%2Fapi%2Ftext-to-speech%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftext-to-speech%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftext-to-speech%2Froute&page=%2Fapi%2Ftext-to-speech%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftext-to-speech%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Ahmed_Downloads_kokoro_main_kokoro_main_nicole_stories_app_app_api_text_to_speech_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/text-to-speech/route.ts */ \"(rsc)/./app/api/text-to-speech/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/text-to-speech/route\",\n        pathname: \"/api/text-to-speech\",\n        filename: \"route\",\n        bundlePath: \"app/api/text-to-speech/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\api\\\\text-to-speech\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Ahmed_Downloads_kokoro_main_kokoro_main_nicole_stories_app_app_api_text_to_speech_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/text-to-speech/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftext-to-speech%2Froute&page=%2Fapi%2Ftext-to-speech%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftext-to-speech%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/text-to-speech/route.ts":
/*!*****************************************!*\
  !*** ./app/api/text-to-speech/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var replicate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! replicate */ \"(rsc)/./node_modules/replicate/index.js\");\n/* harmony import */ var replicate__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(replicate__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst replicate = new (replicate__WEBPACK_IMPORTED_MODULE_1___default())({\n    auth: process.env.REPLICATE_API_TOKEN\n});\n// Fallback to Google Colab if available\nasync function tryGoogleColabTTS(text, voice, speed) {\n    try {\n        // This would be your Google Colab endpoint\n        const colabEndpoint = process.env.GOOGLE_COLAB_ENDPOINT;\n        if (!colabEndpoint) {\n            throw new Error(\"Google Colab endpoint not configured\");\n        }\n        const response = await fetch(colabEndpoint, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                text,\n                voice,\n                speed\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Google Colab request failed\");\n        }\n        const data = await response.json();\n        return data.audioUrl;\n    } catch (error) {\n        console.error(\"Google Colab TTS failed:\", error);\n        throw error;\n    }\n}\n// Replicate TTS function\nasync function tryReplicateTTS(text, voice, speed) {\n    try {\n        const output = await replicate.run(\"lucataco/kokoro-82m:9d3e3b0b8b2b4b4b8b2b4b4b8b2b4b4b8b2b4b4b\", {\n            input: {\n                text: text,\n                voice: voice,\n                speed: speed\n            }\n        });\n        return output;\n    } catch (error) {\n        console.error(\"Replicate TTS failed:\", error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const { text, voice = \"af_nicole\", speed = 0.85 } = await request.json();\n        if (!text) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Text is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate text length (max 5000 characters for reasonable processing)\n        if (text.length > 5000) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Text too long. Maximum 5000 characters allowed.\"\n            }, {\n                status: 400\n            });\n        }\n        let audioUrl;\n        // Try Replicate first\n        if (process.env.REPLICATE_API_TOKEN) {\n            try {\n                console.log(\"Trying Replicate TTS...\");\n                audioUrl = await tryReplicateTTS(text, voice, speed);\n            } catch (error) {\n                console.log(\"Replicate failed, trying Google Colab...\");\n                audioUrl = await tryGoogleColabTTS(text, voice, speed);\n            }\n        } else {\n            // Try Google Colab\n            console.log(\"Trying Google Colab TTS...\");\n            audioUrl = await tryGoogleColabTTS(text, voice, speed);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            audioUrl,\n            voice,\n            speed,\n            textLength: text.length\n        });\n    } catch (error) {\n        console.error(\"TTS Error:\", error);\n        // Return a helpful error message\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Text-to-speech service temporarily unavailable\",\n            message: \"Please try again in a few moments. If the problem persists, check your API configuration.\",\n            fallback: \"You can copy the text and use Google Translate's text-to-speech feature as an alternative.\"\n        }, {\n            status: 503\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: \"Text-to-Speech API is running\",\n        supportedVoices: [\n            \"af_nicole\",\n            \"af_sarah\",\n            \"am_adam\",\n            \"am_michael\"\n        ],\n        speedRange: \"0.5 - 2.0\",\n        maxTextLength: 5000,\n        endpoints: {\n            \"POST /api/text-to-speech\": {\n                description: \"Convert text to speech\",\n                parameters: {\n                    text: \"string (required, max 5000 chars)\",\n                    voice: \"string (optional, default: af_nicole)\",\n                    speed: \"number (optional, default: 0.85, range: 0.5-2.0)\"\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/text-to-speech/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/event-target-shim","vendor-chunks/abort-controller","vendor-chunks/readable-stream","vendor-chunks/replicate","vendor-chunks/string_decoder","vendor-chunks/safe-buffer","vendor-chunks/process"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftext-to-speech%2Froute&page=%2Fapi%2Ftext-to-speech%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftext-to-speech%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();