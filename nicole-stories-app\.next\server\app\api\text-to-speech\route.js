"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/text-to-speech/route";
exports.ids = ["app/api/text-to-speech/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftext-to-speech%2Froute&page=%2Fapi%2Ftext-to-speech%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftext-to-speech%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftext-to-speech%2Froute&page=%2Fapi%2Ftext-to-speech%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftext-to-speech%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Ahmed_Downloads_kokoro_main_kokoro_main_nicole_stories_app_app_api_text_to_speech_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/text-to-speech/route.ts */ \"(rsc)/./app/api/text-to-speech/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/text-to-speech/route\",\n        pathname: \"/api/text-to-speech\",\n        filename: \"route\",\n        bundlePath: \"app/api/text-to-speech/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\api\\\\text-to-speech\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Ahmed_Downloads_kokoro_main_kokoro_main_nicole_stories_app_app_api_text_to_speech_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/text-to-speech/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftext-to-speech%2Froute&page=%2Fapi%2Ftext-to-speech%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftext-to-speech%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/text-to-speech/route.ts":
/*!*****************************************!*\
  !*** ./app/api/text-to-speech/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var replicate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! replicate */ \"(rsc)/./node_modules/replicate/index.js\");\n/* harmony import */ var replicate__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(replicate__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst replicate = new (replicate__WEBPACK_IMPORTED_MODULE_1___default())({\n    auth: \"****************************************\"\n});\n// Fallback to Google Colab if available\nasync function tryGoogleColabTTS(text, voice, speed) {\n    try {\n        // This would be your Google Colab endpoint\n        const colabEndpoint = process.env.GOOGLE_COLAB_ENDPOINT;\n        if (!colabEndpoint) {\n            throw new Error(\"Google Colab endpoint not configured\");\n        }\n        const response = await fetch(colabEndpoint, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                text,\n                voice,\n                speed\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Google Colab request failed\");\n        }\n        const data = await response.json();\n        return data.audioUrl;\n    } catch (error) {\n        console.error(\"Google Colab TTS failed:\", error);\n        throw error;\n    }\n}\n// Replicate TTS function\nasync function tryReplicateTTS(text, voice, speed) {\n    try {\n        const output = await replicate.run(\"lucataco/kokoro-82m:9d3e3b0b8b2b4b4b8b2b4b4b8b2b4b4b8b2b4b4b\", {\n            input: {\n                text: text,\n                voice: voice,\n                speed: speed\n            }\n        });\n        return output;\n    } catch (error) {\n        console.error(\"Replicate TTS failed:\", error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const { text, voice = \"af_nicole\", speed = 0.85 } = await request.json();\n        if (!text) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Text is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate text length (max 5000 characters for reasonable processing)\n        if (text.length > 5000) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Text too long. Maximum 5000 characters allowed.\"\n            }, {\n                status: 400\n            });\n        }\n        let audioUrl;\n        // Try Replicate first\n        if (true) {\n            try {\n                console.log(\"Trying Replicate TTS...\");\n                audioUrl = await tryReplicateTTS(text, voice, speed);\n            } catch (error) {\n                console.log(\"Replicate failed, trying Google Colab...\");\n                audioUrl = await tryGoogleColabTTS(text, voice, speed);\n            }\n        } else {}\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            audioUrl,\n            voice,\n            speed,\n            textLength: text.length\n        });\n    } catch (error) {\n        console.error(\"TTS Error:\", error);\n        // Return a helpful error message\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Text-to-speech service temporarily unavailable\",\n            message: \"Please try again in a few moments. If the problem persists, check your API configuration.\",\n            fallback: \"You can copy the text and use Google Translate's text-to-speech feature as an alternative.\"\n        }, {\n            status: 503\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: \"Text-to-Speech API is running\",\n        supportedVoices: [\n            \"af_nicole\",\n            \"af_sarah\",\n            \"am_adam\",\n            \"am_michael\"\n        ],\n        speedRange: \"0.5 - 2.0\",\n        maxTextLength: 5000,\n        endpoints: {\n            \"POST /api/text-to-speech\": {\n                description: \"Convert text to speech\",\n                parameters: {\n                    text: \"string (required, max 5000 chars)\",\n                    voice: \"string (optional, default: af_nicole)\",\n                    speed: \"number (optional, default: 0.85, range: 0.5-2.0)\"\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/text-to-speech/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/event-target-shim","vendor-chunks/abort-controller","vendor-chunks/readable-stream","vendor-chunks/replicate","vendor-chunks/string_decoder","vendor-chunks/safe-buffer","vendor-chunks/process"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftext-to-speech%2Froute&page=%2Fapi%2Ftext-to-speech%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftext-to-speech%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();