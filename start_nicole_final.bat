@echo off
title Nicole TTS - Final Solution
color 0B

echo.
echo ========================================
echo 🎧 Nicole TTS - Complete Solution
echo ========================================
echo 🇺🇸 🚺 American Female Voice for Stories
echo ========================================
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 تحميل Python من: https://python.org
    start https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM Install packages
echo 📦 تثبيت المكتبات...
pip install requests tkinter >nul 2>&1

echo.
echo 🎯 اختر طريقة الاستخدام:
echo.
echo 1. 🖥️  واجهة سطح المكتب (Desktop App)
echo 2. 💻 سطر الأوامر البسيط (Simple CLI)
echo 3. 🌐 فتح HuggingFace مباشرة (Direct)
echo 4. 📋 عرض تعليمات n8n
echo 5. 🔧 اختبار الاتصال
echo 6. ❌ خروج
echo.

set /p choice="اختر رقم (1-6): "

if "%choice%"=="1" goto desktop
if "%choice%"=="2" goto simple
if "%choice%"=="3" goto direct
if "%choice%"=="4" goto n8n_help
if "%choice%"=="5" goto test
if "%choice%"=="6" goto exit
goto invalid

:desktop
echo.
echo 🖥️ تشغيل واجهة سطح المكتب...
echo 💡 ستفتح نافذة جديدة مع واجهة Nicole TTS
python nicole_desktop_app.py
goto end

:simple
echo.
echo 💻 تشغيل الإصدار البسيط...
python nicole_simple.py
goto end

:direct
echo.
echo 🌐 فتح HuggingFace Spaces مباشرة...
echo ✅ سيتم فتح موقعين في المتصفح
start https://aiqcamp-mcp-kokoro.hf.space
timeout /t 2 /nobreak >nul
start https://webml-community-kokoro-web.hf.space
echo.
echo 📋 تعليمات الاستخدام:
echo 1. اكتب قصتك في الموقع
echo 2. اختر "af_nicole" للصوت
echo 3. اضبط السرعة على 0.85
echo 4. اضغط Submit
echo.
echo 💡 إذا كان الموقع الأول مشغول، جرب الثاني
goto end

:n8n_help
echo.
echo 📋 تعليمات استخدام Nicole TTS مع n8n:
echo ========================================
echo.
echo 🔧 HTTP Request Node Settings:
echo URL: https://aiqcamp-mcp-kokoro.hf.space/api/predict
echo Method: POST
echo Content-Type: application/json
echo.
echo 📝 Body (JSON):
echo {
echo   "data": [
echo     "Your story text here",
echo     "af_nicole",
echo     0.85
echo   ]
echo }
echo.
echo 🎵 Response:
echo response.body.data[0].url = Audio URL
echo.
echo 💡 Code Node Example:
echo const response = await $http.request({
echo   method: 'POST',
echo   url: 'https://aiqcamp-mcp-kokoro.hf.space/api/predict',
echo   headers: { 'Content-Type': 'application/json' },
echo   body: { data: [items[0].json.text, "af_nicole", 0.85] }
echo });
echo return { audioUrl: response.body.data[0].url };
echo.
goto end

:test
echo.
echo 🔧 اختبار الاتصال مع HuggingFace...
echo.
python -c "
import requests
import sys

endpoints = [
    'https://aiqcamp-mcp-kokoro.hf.space',
    'https://webml-community-kokoro-web.hf.space'
]

for endpoint in endpoints:
    try:
        print(f'🔄 اختبار: {endpoint}')
        response = requests.get(endpoint, timeout=10)
        if response.status_code == 200:
            print(f'✅ متاح: {endpoint}')
        else:
            print(f'⚠️  استجابة: {response.status_code}')
    except Exception as e:
        print(f'❌ غير متاح: {endpoint}')

print('\n💡 إذا كانت الخدمات متاحة، يمكنك استخدام Nicole TTS')
"
goto end

:invalid
echo.
echo ❌ اختيار غير صحيح!
timeout /t 2 /nobreak >nul
goto start

:exit
echo.
echo 👋 شكر<|im_start|> لاستخدام Nicole TTS!
goto end

:end
echo.
echo ========================================
echo 🎧 Nicole TTS Session Ended
echo ========================================
pause
