#!/usr/bin/env python3
"""
Simple Nicole TTS - Direct HuggingFace Integration
"""

from flask import Flask, request, jsonify, render_template_string
import requests
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Nicole TTS - Simple</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .container { background: #f9f9f9; padding: 30px; border-radius: 10px; }
        h1 { color: #333; text-align: center; }
        textarea { width: 100%; height: 150px; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; }
        button { width: 100%; padding: 15px; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin-top: 20px; padding: 20px; background: #e9f7ef; border-radius: 5px; display: none; }
        .loading { text-align: center; margin: 20px 0; display: none; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; display: none; }
        audio { width: 100%; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎧 Nicole TTS - American Female Voice</h1>
        <p style="text-align: center; color: #666;">Perfect for bedtime stories and audiobooks</p>
        
        <form id="ttsForm">
            <label for="text">📝 Enter your text:</label>
            <textarea id="text" placeholder="Once upon a time, in a magical forest..." required>Once upon a time, in a magical forest, there lived a little rabbit named Luna who loved to tell bedtime stories.</textarea>
            
            <button type="submit" id="generateBtn">🎵 Generate Nicole's Voice</button>
        </form>
        
        <div class="loading" id="loading">
            <p>🎧 Nicole is creating your audio... Please wait (30-60 seconds)</p>
        </div>
        
        <div class="error" id="error"></div>
        
        <div class="result" id="result">
            <h3>✅ Audio Ready!</h3>
            <audio controls id="audioPlayer"></audio>
            <p id="resultInfo"></p>
        </div>
    </div>

    <script>
        document.getElementById('ttsForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const text = document.getElementById('text').value.trim();
            if (!text) {
                alert('Please enter some text!');
                return;
            }
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            document.getElementById('generateBtn').disabled = true;
            
            try {
                const response = await fetch('/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: text })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('audioPlayer').src = result.audio_url;
                    document.getElementById('resultInfo').innerHTML = 
                        `<strong>Voice:</strong> Nicole 🇺🇸 🚺<br>
                         <strong>Length:</strong> ${result.text_length} characters<br>
                         <strong>Provider:</strong> ${result.provider}`;
                    document.getElementById('result').style.display = 'block';
                } else {
                    throw new Error(result.error || 'Failed to generate audio');
                }
            } catch (error) {
                document.getElementById('error').textContent = 'Error: ' + error.message;
                document.getElementById('error').style.display = 'block';
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('generateBtn').disabled = false;
            }
        });
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    return HTML_TEMPLATE

@app.route('/generate', methods=['POST'])
def generate_audio():
    try:
        data = request.get_json()
        text = data.get('text', '').strip()
        
        if not text:
            return jsonify({'success': False, 'error': 'No text provided'})
        
        logger.info(f"Generating audio for {len(text)} characters")
        
        # Try HuggingFace Spaces
        endpoints = [
            'https://aiqcamp-mcp-kokoro.hf.space/api/predict',
            'https://webml-community-kokoro-web.hf.space/api/predict'
        ]
        
        for endpoint in endpoints:
            try:
                logger.info(f"Trying endpoint: {endpoint}")
                
                if 'mcp-kokoro' in endpoint:
                    payload = {'data': [text, 'af_nicole', 0.85]}
                else:
                    payload = {'data': [text, 'af_nicole', 0.85, False]}
                
                response = requests.post(
                    endpoint,
                    json=payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=120
                )
                
                logger.info(f"Response status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"Response data: {result}")
                    
                    if result.get('data') and result['data'][0]:
                        audio_data = result['data'][0]
                        
                        if isinstance(audio_data, dict):
                            audio_url = audio_data.get('url')
                        else:
                            audio_url = audio_data
                        
                        if audio_url:
                            if audio_url.startswith('/'):
                                base_url = endpoint.replace('/api/predict', '')
                                audio_url = base_url + audio_url
                            
                            logger.info(f"✅ Success! Audio URL: {audio_url}")
                            
                            return jsonify({
                                'success': True,
                                'audio_url': audio_url,
                                'text_length': len(text),
                                'provider': endpoint.split('//')[1].split('.')[0]
                            })
                
            except Exception as e:
                logger.warning(f"Endpoint {endpoint} failed: {e}")
                continue
        
        # If all endpoints fail
        return jsonify({
            'success': False,
            'error': 'All TTS services are currently unavailable. Please try again later.'
        })
        
    except Exception as e:
        logger.error(f"Generate audio error: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/predict', methods=['POST'])
def api_predict():
    """n8n compatible endpoint"""
    try:
        data = request.get_json()
        text = data['data'][0] if data.get('data') else ''
        
        # Forward to generate endpoint
        gen_response = requests.post(
            'http://localhost:5002/generate',
            json={'text': text}
        )
        
        if gen_response.status_code == 200:
            gen_data = gen_response.json()
            if gen_data.get('success'):
                return jsonify({
                    'data': [{'url': gen_data['audio_url']}]
                })
        
        return jsonify({'error': 'TTS service unavailable'}), 503
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🎧 Starting Simple Nicole TTS...")
    print("🌐 Web Interface: http://localhost:5002")
    print("📡 API for n8n: http://localhost:5002/api/predict")
    print("🇺🇸 🚺 Nicole is ready!")
    
    app.run(host='0.0.0.0', port=5002, debug=True)
