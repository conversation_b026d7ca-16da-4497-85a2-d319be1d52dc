{"name": "Kokoro TTS API Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "webhook-tts", "responseMode": "responseNode", "options": {}}, "id": "webhook-start", "name": "Webhook Start", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "kokoro-tts-webhook"}, {"parameters": {"url": "http://localhost:8000/tts_url", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.text || 'Hello from Kokoro TTS!' }}"}, {"name": "voice", "value": "={{ $json.voice || 'af_nicole' }}"}, {"name": "speed", "value": "={{ $json.speed || 1.0 }}"}]}, "options": {}}, "id": "kokoro-api-call", "name": "Kokoro TTS API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook Start": {"main": [[{"node": "Kokoro TTS API", "type": "main", "index": 0}]]}, "Kokoro TTS API": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "kokoro-tts-workflow", "tags": []}