#!/usr/bin/env python3
"""
Nicole Local TTS - Windows Built-in Voice
استخدام أصوات Windows المدمجة
"""

import pyttsx3
import os
import sys

def setup_nicole_voice():
    """إعداد صوت Nicole باستخدام Windows TTS"""
    try:
        # إنشاء محرك TTS
        engine = pyttsx3.init()
        
        # الحصول على الأصوات المتاحة
        voices = engine.getProperty('voices')
        
        print("🎧 البحث عن أصوات أنثوية أمريكية...")
        print("=" * 50)
        
        # البحث عن أفضل صوت أنثوي
        female_voices = []
        for i, voice in enumerate(voices):
            if voice.name:
                print(f"{i}: {voice.name}")
                if any(keyword in voice.name.lower() for keyword in ['female', 'woman', 'zira', 'hazel', 'aria']):
                    female_voices.append((i, voice))
        
        if female_voices:
            # اختيار أفضل صوت
            best_voice = female_voices[0][1]
            engine.setProperty('voice', best_voice.id)
            print(f"\n✅ تم اختيار: {best_voice.name}")
        else:
            print("\n⚠️ لم يتم العثور على صوت أنثوي، سيتم استخدام الصوت الافتراضي")
        
        # إعداد السرعة والصوت
        engine.setProperty('rate', 150)  # سرعة مناسبة للقصص
        engine.setProperty('volume', 0.9)  # مستوى صوت عالي
        
        return engine
        
    except Exception as e:
        print(f"❌ خطأ في إعداد المحرك: {e}")
        return None

def read_story(engine, text):
    """قراءة القصة"""
    try:
        print("\n🎧 Nicole تقرأ القصة...")
        print("🔊 تأكد من تشغيل الصوت")
        
        engine.say(text)
        engine.runAndWait()
        
        print("✅ انتهت القراءة!")
        
    except Exception as e:
        print(f"❌ خطأ في القراءة: {e}")

def save_audio(engine, text, filename="nicole_story.wav"):
    """حفظ الصوت كملف"""
    try:
        print(f"\n💾 حفظ الصوت في: {filename}")
        
        engine.save_to_file(text, filename)
        engine.runAndWait()
        
        if os.path.exists(filename):
            print(f"✅ تم الحفظ: {os.path.abspath(filename)}")
            return True
        else:
            print("❌ فشل في الحفظ")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الحفظ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎧 Nicole Local TTS")
    print("=" * 50)
    
    # النص التجريبي
    story_text = """Hello, my name is Nicole. I will read you a beautiful bedtime story tonight.

Once upon a time, in a magical forest far away, there lived a little rabbit named Luna. Every night before bed, Luna would look up at the twinkling stars and make a wish for sweet dreams and wonderful adventures.

One evening, as Luna was getting ready for sleep, she noticed a gentle glow coming from behind the old oak tree. The fairy began to read about a land where flowers sang lullabies, where clouds were made of cotton candy, and where every child's wish came true under the starlight.

And now, just like Luna, it's time for you to close your eyes and let your own beautiful dreams begin. Good night, sweet dreams."""
    
    # إعداد المحرك
    engine = setup_nicole_voice()
    if not engine:
        print("❌ فشل في إعداد محرك TTS")
        input("اضغط Enter للخروج...")
        return
    
    while True:
        print("\n🎯 اختر العملية:")
        print("1. 🎧 استماع للقصة")
        print("2. 💾 حفظ الصوت كملف")
        print("3. 📝 تخصيص النص")
        print("4. ❌ خروج")
        
        choice = input("\nاختر رقم (1-4): ").strip()
        
        if choice == "1":
            read_story(engine, story_text)
            
        elif choice == "2":
            filename = input("اسم الملف (اتركه فارغ للافتراضي): ").strip()
            if not filename:
                filename = "nicole_story.wav"
            if not filename.endswith('.wav'):
                filename += '.wav'
            
            if save_audio(engine, story_text, filename):
                play_choice = input("هل تريد تشغيل الملف؟ (y/n): ").strip().lower()
                if play_choice in ['y', 'yes', 'نعم']:
                    os.startfile(filename)
                    
        elif choice == "3":
            print("\n📝 اكتب النص الجديد (اتركه فارغ للإلغاء):")
            custom_text = input().strip()
            if custom_text:
                story_text = custom_text
                print("✅ تم تحديث النص")
            else:
                print("❌ لم يتم التغيير")
                
        elif choice == "4":
            print("👋 وداعاً!")
            break
            
        else:
            print("❌ اختيار غير صحيح!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم الإلغاء")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
    
    input("\nاضغط Enter للخروج...")
