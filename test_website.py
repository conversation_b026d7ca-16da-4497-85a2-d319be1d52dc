#!/usr/bin/env python3
"""
اختبار موقع Kokoro TTS
يختبر جميع endpoints ويتأكد من عمل الموقع بشكل صحيح
"""

import requests
import json
import time
import sys
from pathlib import Path

# إعدادات الاختبار
BASE_URL = 'http://localhost:5000'
TEST_TEXT = "Hello, this is a test of the Kokoro TTS system. <PERSON>'s voice should sound clear and natural."
TEST_VOICE = 'af_nicole'
TEST_SPEED = 0.9

def test_health_check():
    """اختبار health check"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f'{BASE_URL}/health', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_voices_endpoint():
    """اختبار voices endpoint"""
    print("\n🎤 Testing voices endpoint...")
    try:
        response = requests.get(f'{BASE_URL}/voices', timeout=10)
        if response.status_code == 200:
            data = response.json()
            voices = data.get('voices', {})
            print(f"✅ Found {len(voices)} voices:")
            for voice_id, voice_info in voices.items():
                name = voice_info.get('name', voice_id) if isinstance(voice_info, dict) else voice_info
                print(f"   - {voice_id}: {name}")
            return True
        else:
            print(f"❌ Voices endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Voices endpoint error: {e}")
        return False

def test_stats_endpoint():
    """اختبار stats endpoint"""
    print("\n📊 Testing stats endpoint...")
    try:
        response = requests.get(f'{BASE_URL}/stats', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Stats retrieved:")
            print(f"   - Total requests: {data.get('total_requests', 0)}")
            print(f"   - Total characters: {data.get('total_characters', 0)}")
            print(f"   - Uptime hours: {data.get('uptime_hours', 0)}")
            return True
        else:
            print(f"❌ Stats endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Stats endpoint error: {e}")
        return False

def test_api_predict():
    """اختبار API predict endpoint (Gradio format)"""
    print(f"\n🎯 Testing API predict with text: '{TEST_TEXT[:50]}...'")
    try:
        payload = {
            "data": [TEST_TEXT, TEST_VOICE, TEST_SPEED]
        }
        
        print(f"📤 Sending request to {BASE_URL}/api/predict...")
        response = requests.post(
            f'{BASE_URL}/api/predict',
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                audio_url = data['data'][0].get('url')
                duration = data.get('duration', 0)
                print(f"✅ API predict successful:")
                print(f"   - Audio URL: {audio_url}")
                print(f"   - Duration: {duration:.2f} seconds")
                print(f"   - Voice: {data.get('voice')}")
                
                # اختبار تحميل الملف الصوتي
                if audio_url:
                    return test_audio_download(audio_url)
                return True
            else:
                print(f"❌ API predict failed: {data}")
                return False
        else:
            print(f"❌ API predict failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ API predict error: {e}")
        return False

def test_audio_download(audio_url):
    """اختبار تحميل الملف الصوتي"""
    print(f"\n🎵 Testing audio download from: {audio_url}")
    try:
        response = requests.get(audio_url, timeout=30)
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            content_length = len(response.content)
            print(f"✅ Audio download successful:")
            print(f"   - Content type: {content_type}")
            print(f"   - File size: {content_length} bytes")
            
            # حفظ ملف تجريبي
            test_file = Path('test_audio.wav')
            with open(test_file, 'wb') as f:
                f.write(response.content)
            print(f"   - Saved test file: {test_file}")
            
            return True
        else:
            print(f"❌ Audio download failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Audio download error: {e}")
        return False

def test_tts_url_endpoint():
    """اختبار TTS URL endpoint"""
    print(f"\n🔗 Testing TTS URL endpoint...")
    try:
        payload = {
            "text": "This is a test of the TTS URL endpoint.",
            "voice": TEST_VOICE,
            "speed": TEST_SPEED
        }
        
        response = requests.post(
            f'{BASE_URL}/tts_url',
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ TTS URL endpoint successful:")
                print(f"   - Audio URL: {data.get('audioUrl')}")
                print(f"   - Duration: {data.get('duration', 0):.2f} seconds")
                return True
            else:
                print(f"❌ TTS URL endpoint failed: {data}")
                return False
        else:
            print(f"❌ TTS URL endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ TTS URL endpoint error: {e}")
        return False

def test_web_interface():
    """اختبار الواجهة الرئيسية"""
    print(f"\n🌐 Testing web interface...")
    try:
        response = requests.get(BASE_URL, timeout=10)
        if response.status_code == 200:
            content = response.text
            if 'Kokoro TTS' in content and 'Generate Speech' in content:
                print(f"✅ Web interface loaded successfully")
                print(f"   - Page size: {len(content)} characters")
                return True
            else:
                print(f"❌ Web interface content invalid")
                return False
        else:
            print(f"❌ Web interface failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Web interface error: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 Starting Kokoro TTS Website Tests")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_check),
        ("Web Interface", test_web_interface),
        ("Voices Endpoint", test_voices_endpoint),
        ("Stats Endpoint", test_stats_endpoint),
        ("API Predict", test_api_predict),
        ("TTS URL Endpoint", test_tts_url_endpoint)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        time.sleep(1)  # انتظار ثانية بين الاختبارات
    
    print("\n" + "="*50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your Kokoro website is working perfectly!")
        print(f"\n🌐 Access your website at: {BASE_URL}")
        print(f"📡 API endpoint for n8n: {BASE_URL}/api/predict")
    else:
        print(f"⚠️  {total - passed} tests failed. Check the errors above.")
        print("💡 Make sure your Kokoro server is running with: start_kokoro_server.bat")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
