# إعداد Kokoro في n8n باستخدام HTTP Request

## الطريقة الأولى: HTTP Request Node

### إعدادات العقدة:

**Node Type:** HTTP Request
**Method:** POST
**URL:** `https://aiqcamp-mcp-kokoro.hf.space/api/predict`

### Headers:
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

### Body (JSON):
```json
{
  "data": [
    "{{ $json.text }}",
    "af_nicole",
    0.9
  ]
}
```

### Response Processing:
أضف **Function** node بعد HTTP Request:

```javascript
// معالجة استجابة Kokoro
const response = $input.item.json;

if (response.data && response.data[0]) {
  let audioUrl = response.data[0].url || response.data[0];
  
  // التأكد من الرابط الكامل
  if (audioUrl.startsWith('/')) {
    audioUrl = 'https://aiqcamp-mcp-kokoro.hf.space' + audioUrl;
  }
  
  return {
    json: {
      success: true,
      audioUrl: audioUrl,
      voice: 'af_nicole'
    }
  };
} else {
  return {
    json: {
      success: false,
      error: 'No audio URL received'
    }
  };
}
```

## الطريقة الثانية: Code Node (الأفضل)

استخدم الكود من `n8n_kokoro_simple.js` في **Code** node واحدة.

## الطريقة الثالثة: مع Fallback

استخدم الكود من `n8n_kokoro_mcp.js` للحصول على نظام احتياطي.

## اختبار الإعداد:

### نص تجريبي:
```
"Once upon a time, in a magical forest far away, there lived a little rabbit named Luna. Every night before bed, Luna would look up at the twinkling stars and make a wish for sweet dreams and wonderful adventures."
```

### النتيجة المتوقعة:
- **audioUrl**: رابط ملف WAV
- **voice**: af_nicole
- **success**: true

## استكشاف الأخطاء:

### إذا فشل الطلب:
1. تحقق من الاتصال بالإنترنت
2. جرب نص أقصر
3. استخدم الـ fallback إلى kokoro-web

### إذا لم يظهر الصوت:
1. تحقق من صحة رابط الصوت
2. جرب تحميل الملف مباشرة
3. تأكد من تشغيل الصوت في المتصفح

## نصائح للحصول على أفضل نتيجة:

1. **استخدم جمل واضحة** ومفهومة
2. **تجنب النصوص الطويلة جداً** (أكثر من 1000 كلمة)
3. **استخدم علامات الترقيم** للحصول على توقفات طبيعية
4. **اختبر مع نص قصير** أولاً

## الأصوات المتاحة:

- **af_nicole**: 🇺🇸 🚺 Nicole 🎧 (الأفضل للقصص)
- **af_heart**: 🇺🇸 🚺 Heart ❤️
- **af_bella**: 🇺🇸 🚺 Bella 🔥
- **am_michael**: 🇺🇸 🚹 Michael
- **bf_emma**: 🇬🇧 🚺 Emma
- **bm_george**: 🇬🇧 🚹 George
