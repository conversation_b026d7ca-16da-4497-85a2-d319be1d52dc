#!/usr/bin/env python3
"""
Nicole TTS - Simple Version
حل بسيط وسريع لـ Nicole TTS
"""

import requests
import webbrowser
import os
from datetime import datetime

def nicole_tts_simple():
    print("🎧 Nicole TTS - Simple Version")
    print("=" * 50)
    
    # النص التجريبي
    default_text = """Once upon a time, in a magical forest far away, there lived a little rabbit named <PERSON>. Every night before bed, <PERSON> would look up at the twinkling stars and make a wish for sweet dreams and wonderful adventures."""
    
    print("📖 النص الافتراضي:")
    print(default_text)
    print("\n" + "=" * 50)
    
    # خيارات المستخدم
    print("🎯 اختر طريقة الاستخدام:")
    print("1. 🤖 محاولة تلقائية مع HuggingFace")
    print("2. 🌐 فتح HuggingFace مباشرة (الأفضل)")
    print("3. 📝 تخصيص النص")
    print("4. ❌ خروج")
    
    choice = input("\nاختر رقم (1-4): ").strip()
    
    if choice == "1":
        try_automatic(default_text)
    elif choice == "2":
        open_huggingface_direct(default_text)
    elif choice == "3":
        custom_text = input("\n📝 اكتب نصك: ").strip()
        if custom_text:
            try_automatic(custom_text)
        else:
            print("❌ لم تدخل أي نص!")
    elif choice == "4":
        print("👋 وداع<|im_start|>!")
    else:
        print("❌ اختيار غير صحيح!")

def try_automatic(text):
    """محاولة تلقائية مع HuggingFace"""
    print(f"\n🎧 Nicole تحاول قراءة: {text[:50]}...")
    
    endpoints = [
        {
            'url': 'https://aiqcamp-mcp-kokoro.hf.space/api/predict',
            'name': 'MCP-Kokoro',
            'payload': {'data': [text, 'af_nicole', 0.85]}
        },
        {
            'url': 'https://webml-community-kokoro-web.hf.space/api/predict',
            'name': 'WebML-Kokoro', 
            'payload': {'data': [text, 'af_nicole', 0.85, False]}
        }
    ]
    
    for i, endpoint in enumerate(endpoints, 1):
        try:
            print(f"🔄 المحاولة {i}/{len(endpoints)}: {endpoint['name']}")
            
            response = requests.post(
                endpoint['url'],
                json=endpoint['payload'],
                headers={'Content-Type': 'application/json'},
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('data') and result['data'][0]:
                    audio_data = result['data'][0]
                    audio_url = audio_data.get('url') if isinstance(audio_data, dict) else audio_data
                    
                    if audio_url and audio_url.startswith('/'):
                        base_url = endpoint['url'].replace('/api/predict', '')
                        audio_url = base_url + audio_url
                    
                    if audio_url:
                        print(f"✅ نجح! تم إنتاج الصوت")
                        print(f"🌐 رابط الصوت: {audio_url}")
                        print(f"🎤 الصوت: Nicole 🇺🇸 🚺")
                        print(f"🏢 المزود: {endpoint['name']}")
                        
                        # فتح الصوت في المتصفح
                        print("🌐 فتح الصوت في المتصفح...")
                        webbrowser.open(audio_url)
                        
                        # خيار التحميل
                        download = input("\n📥 هل تريد تحميل الصوت؟ (y/n): ").strip().lower()
                        if download in ['y', 'yes', 'نعم']:
                            download_audio(audio_url, text)
                        
                        return True
            
        except Exception as e:
            print(f"❌ فشل {endpoint['name']}: {str(e)[:50]}...")
            continue
    
    print("\n❌ فشلت جميع المحاولات!")
    print("💡 جرب الطريقة اليدوية...")
    
    manual = input("🌐 هل تريد فتح HuggingFace مباشرة؟ (y/n): ").strip().lower()
    if manual in ['y', 'yes', 'نعم']:
        open_huggingface_direct(text)
    
    return False

def open_huggingface_direct(text):
    """فتح HuggingFace مباشرة"""
    print("\n🌐 فتح HuggingFace Spaces...")
    
    urls = [
        'https://aiqcamp-mcp-kokoro.hf.space',
        'https://webml-community-kokoro-web.hf.space'
    ]
    
    for url in urls:
        webbrowser.open(url)
    
    print("✅ تم فتح HuggingFace Spaces في المتصفح")
    print("\n📋 تعليمات الاستخدام:")
    print("1. انسخ النص التالي:")
    print(f"   {text}")
    print("2. الصقه في HuggingFace")
    print("3. اختر 'af_nicole' للصوت")
    print("4. اضبط السرعة على 0.85")
    print("5. اضغط Submit")
    print("\n💡 إذا كان الموقع الأول مشغول، جرب الثاني")
    
    # نسخ النص للحافظة (إذا أمكن)
    try:
        import pyperclip
        pyperclip.copy(text)
        print("📋 تم نسخ النص للحافظة!")
    except:
        print("📋 انسخ النص يدوي<|im_start|> من أعلاه")

def download_audio(audio_url, text):
    """تحميل الصوت"""
    try:
        print("📥 جاري تحميل الصوت...")
        
        # إنشاء اسم ملف
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"nicole_story_{timestamp}.wav"
        
        # تحميل الصوت
        response = requests.get(audio_url, timeout=60)
        if response.status_code == 200:
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ تم التحميل: {filename}")
            print(f"📁 المجلد: {os.path.abspath(filename)}")
            
            # فتح المجلد
            open_folder = input("📂 هل تريد فتح المجلد؟ (y/n): ").strip().lower()
            if open_folder in ['y', 'yes', 'نعم']:
                os.startfile(os.path.dirname(os.path.abspath(filename)))
        else:
            print(f"❌ فشل التحميل: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في التحميل: {e}")

def main():
    """الدالة الرئيسية"""
    try:
        nicole_tts_simple()
    except KeyboardInterrupt:
        print("\n\n👋 تم الإلغاء بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
