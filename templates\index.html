<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kokoro TTS - Text to Speech</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .text-input {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.3s;
        }

        .text-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .controls-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }

        .select-input, .range-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            background: white;
        }

        .range-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .range-input {
            flex: 1;
        }

        .range-value {
            min-width: 40px;
            font-weight: 600;
            color: #667eea;
        }

        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result {
            display: none;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .result.show {
            display: block;
        }

        .audio-player {
            width: 100%;
            margin: 15px 0;
        }

        .download-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }

        .error.show {
            display: block;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .voice-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .voice-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s;
        }

        .voice-card:hover {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .voice-card.selected {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .voice-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .voice-details {
            font-size: 0.9rem;
            color: #666;
        }

        @media (max-width: 768px) {
            .controls-row {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .main-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-microphone"></i> Kokoro TTS</h1>
            <p>High-Quality Text-to-Speech with Multiple Voices</p>
        </div>

        <div class="main-card">
            <form id="ttsForm">
                <div class="form-group">
                    <label for="text"><i class="fas fa-edit"></i> Text to Convert:</label>
                    <textarea 
                        id="text" 
                        name="text" 
                        class="text-input" 
                        placeholder="Enter your text here... Perfect for bedtime stories, audiobooks, or any content you want to hear!"
                        required
                    >Once upon a time, in a magical forest far away, there lived a little rabbit named Luna. Every night before bed, Luna would look up at the twinkling stars and make a wish for sweet dreams and wonderful adventures.</textarea>
                </div>

                <div class="controls-row">
                    <div class="form-group">
                        <label for="voice"><i class="fas fa-user"></i> Voice:</label>
                        <select id="voice" name="voice" class="select-input">
                            <option value="af_nicole">🇺🇸 🚺 Nicole 🎧 (Recommended)</option>
                            <option value="af_heart">🇺🇸 🚺 Heart ❤️</option>
                            <option value="af_bella">🇺🇸 🚺 Bella 🔥</option>
                            <option value="am_michael">🇺🇸 🚹 Michael</option>
                            <option value="bf_emma">🇬🇧 🚺 Emma</option>
                            <option value="bm_george">🇬🇧 🚹 George</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="speed"><i class="fas fa-tachometer-alt"></i> Speed:</label>
                        <div class="range-container">
                            <input type="range" id="speed" name="speed" min="0.5" max="2" step="0.1" value="0.9" class="range-input">
                            <span class="range-value" id="speedValue">0.9</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label><i class="fas fa-info-circle"></i> Characters:</label>
                        <div style="padding: 12px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                            <span id="charCount">0</span> / 1000
                        </div>
                    </div>
                </div>

                <button type="submit" class="generate-btn" id="generateBtn">
                    <i class="fas fa-play"></i> Generate Speech
                </button>
            </form>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Generating your audio... Please wait</p>
            </div>

            <div class="result" id="result">
                <h3><i class="fas fa-volume-up"></i> Generated Audio:</h3>
                <audio controls class="audio-player" id="audioPlayer">
                    Your browser does not support the audio element.
                </audio>
                <div>
                    <a href="#" class="download-btn" id="downloadBtn" download="kokoro_audio.wav">
                        <i class="fas fa-download"></i> Download Audio
                    </a>
                </div>
            </div>

            <div class="error" id="error">
                <i class="fas fa-exclamation-triangle"></i> <span id="errorMessage"></span>
            </div>
        </div>

        <div class="stats-card">
            <h2><i class="fas fa-chart-bar"></i> Server Statistics</h2>
            <div class="stats-grid" id="statsGrid">
                <!-- Stats will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Update character count
        const textInput = document.getElementById('text');
        const charCount = document.getElementById('charCount');
        
        function updateCharCount() {
            const count = textInput.value.length;
            charCount.textContent = count;
            charCount.style.color = count > 1000 ? '#dc3545' : '#28a745';
        }
        
        textInput.addEventListener('input', updateCharCount);
        updateCharCount();

        // Update speed value display
        const speedInput = document.getElementById('speed');
        const speedValue = document.getElementById('speedValue');
        
        speedInput.addEventListener('input', function() {
            speedValue.textContent = this.value;
        });

        // Form submission
        document.getElementById('ttsForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                text: formData.get('text'),
                voice: formData.get('voice'),
                speed: parseFloat(formData.get('speed'))
            };

            // Show loading
            document.getElementById('loading').classList.add('show');
            document.getElementById('result').classList.remove('show');
            document.getElementById('error').classList.remove('show');
            document.getElementById('generateBtn').disabled = true;

            try {
                const response = await fetch('/api/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ data: [data.text, data.voice, data.speed] })
                });

                const result = await response.json();

                if (result.success) {
                    // Show result
                    const audioPlayer = document.getElementById('audioPlayer');
                    const downloadBtn = document.getElementById('downloadBtn');
                    
                    audioPlayer.src = result.audioUrl;
                    downloadBtn.href = result.audioUrl;
                    
                    document.getElementById('result').classList.add('show');
                    
                    // Update stats
                    loadStats();
                } else {
                    throw new Error(result.error || 'Unknown error occurred');
                }
            } catch (error) {
                document.getElementById('errorMessage').textContent = error.message;
                document.getElementById('error').classList.add('show');
            } finally {
                document.getElementById('loading').classList.remove('show');
                document.getElementById('generateBtn').disabled = false;
            }
        });

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('/stats');
                const stats = await response.json();
                
                const statsGrid = document.getElementById('statsGrid');
                statsGrid.innerHTML = `
                    <div class="stat-item">
                        <div class="stat-number">${stats.total_requests}</div>
                        <div class="stat-label">Total Requests</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${(stats.total_characters / 1000).toFixed(1)}K</div>
                        <div class="stat-label">Characters Processed</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${(stats.total_audio_duration / 60).toFixed(1)}m</div>
                        <div class="stat-label">Audio Generated</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${stats.uptime_hours}h</div>
                        <div class="stat-label">Server Uptime</div>
                    </div>
                `;
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }

        // Load stats on page load
        loadStats();
        
        // Refresh stats every 30 seconds
        setInterval(loadStats, 30000);
    </script>
</body>
</html>
