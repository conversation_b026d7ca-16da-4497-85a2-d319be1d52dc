/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FobWVkJTVDJTVDRG93bmxvYWRzJTVDJTVDa29rb3JvLW1haW4lNUMlNUNrb2tvcm8tbWFpbiU1QyU1Q25pY29sZS1zdG9yaWVzLWFwcCU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBNkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uaWNvbGUtc3Rvcmllcy1hcHAvPzA4ZmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBaG1lZFxcXFxEb3dubG9hZHNcXFxca29rb3JvLW1haW5cXFxca29rb3JvLW1haW5cXFxcbmljb2xlLXN0b3JpZXMtYXBwXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDownloads%5C%5Ckokoro-main%5C%5Ckokoro-main%5C%5Cnicole-stories-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/components/AudioPlayer.tsx":
/*!****************************************!*\
  !*** ./app/components/AudioPlayer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Pause,Play,SkipBack,SkipForward,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Pause,Play,SkipBack,SkipForward,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/skip-back.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Pause,Play,SkipBack,SkipForward,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Pause,Play,SkipBack,SkipForward,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Pause,Play,SkipBack,SkipForward,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/skip-forward.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Pause,Play,SkipBack,SkipForward,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AudioPlayer({ audioUrl }) {\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        const updateTime = ()=>setCurrentTime(audio.currentTime);\n        const updateDuration = ()=>setDuration(audio.duration);\n        const handleEnded = ()=>setIsPlaying(false);\n        audio.addEventListener(\"timeupdate\", updateTime);\n        audio.addEventListener(\"loadedmetadata\", updateDuration);\n        audio.addEventListener(\"ended\", handleEnded);\n        return ()=>{\n            audio.removeEventListener(\"timeupdate\", updateTime);\n            audio.removeEventListener(\"loadedmetadata\", updateDuration);\n            audio.removeEventListener(\"ended\", handleEnded);\n        };\n    }, [\n        audioUrl\n    ]);\n    const togglePlay = ()=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        if (isPlaying) {\n            audio.pause();\n        } else {\n            audio.play();\n        }\n        setIsPlaying(!isPlaying);\n    };\n    const handleSeek = (e)=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        const newTime = parseFloat(e.target.value);\n        audio.currentTime = newTime;\n        setCurrentTime(newTime);\n    };\n    const handleVolumeChange = (e)=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        const newVolume = parseFloat(e.target.value);\n        audio.volume = newVolume;\n        setVolume(newVolume);\n    };\n    const skipTime = (seconds)=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        audio.currentTime = Math.max(0, Math.min(duration, audio.currentTime + seconds));\n    };\n    const formatTime = (time)=>{\n        const minutes = Math.floor(time / 60);\n        const seconds = Math.floor(time % 60);\n        return `${minutes}:${seconds.toString().padStart(2, \"0\")}`;\n    };\n    const downloadAudio = ()=>{\n        const link = document.createElement(\"a\");\n        link.href = audioUrl;\n        link.download = \"nicole-story.wav\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-a3dd54ba0696360e\" + \" \" + \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a3dd54ba0696360e\" + \" \" + \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"jsx-a3dd54ba0696360e\" + \" \" + \"text-white font-medium flex items-center gap-2\",\n                        children: \"\\uD83C\\uDFA7 Nicole's Voice\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: downloadAudio,\n                        className: \"jsx-a3dd54ba0696360e\" + \" \" + \"flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            \"Download\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: audioRef,\n                src: audioUrl,\n                preload: \"metadata\",\n                className: \"jsx-a3dd54ba0696360e\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a3dd54ba0696360e\" + \" \" + \"flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-a3dd54ba0696360e\" + \" \" + \"audio-visualizer\",\n                    children: [\n                        ...Array(5)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a3dd54ba0696360e\" + \" \" + \"audio-bar\"\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a3dd54ba0696360e\" + \" \" + \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"range\",\n                        min: \"0\",\n                        max: duration || 0,\n                        value: currentTime,\n                        onChange: handleSeek,\n                        className: \"jsx-a3dd54ba0696360e\" + \" \" + \"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-a3dd54ba0696360e\" + \" \" + \"flex justify-between text-white/70 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-a3dd54ba0696360e\",\n                                children: formatTime(currentTime)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-a3dd54ba0696360e\",\n                                children: formatTime(duration)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a3dd54ba0696360e\" + \" \" + \"flex items-center justify-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>skipTime(-10),\n                        className: \"jsx-a3dd54ba0696360e\" + \" \" + \"p-2 text-white/70 hover:text-white transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: togglePlay,\n                        className: \"jsx-a3dd54ba0696360e\" + \" \" + \"p-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full hover:from-blue-600 hover:to-purple-600 transition-all duration-300 shadow-lg\",\n                        children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-6 h-6 ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>skipTime(10),\n                        className: \"jsx-a3dd54ba0696360e\" + \" \" + \"p-2 text-white/70 hover:text-white transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a3dd54ba0696360e\" + \" \" + \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Pause_Play_SkipBack_SkipForward_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4 text-white/70\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"range\",\n                        min: \"0\",\n                        max: \"1\",\n                        step: \"0.1\",\n                        value: volume,\n                        onChange: handleVolumeChange,\n                        className: \"jsx-a3dd54ba0696360e\" + \" \" + \"flex-1 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"a3dd54ba0696360e\",\n                children: \".slider.jsx-a3dd54ba0696360e::-webkit-slider-thumb{-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;width:16px;height:16px;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;background:-webkit-linear-gradient(315deg,#667eea 0%,#764ba2 100%);background:-moz-linear-gradient(315deg,#667eea 0%,#764ba2 100%);background:-o-linear-gradient(315deg,#667eea 0%,#764ba2 100%);background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);cursor:pointer;-webkit-box-shadow:0 2px 4px rgba(0,0,0,.2);-moz-box-shadow:0 2px 4px rgba(0,0,0,.2);box-shadow:0 2px 4px rgba(0,0,0,.2)}.slider.jsx-a3dd54ba0696360e::-moz-range-thumb{width:16px;height:16px;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;background:-webkit-linear-gradient(315deg,#667eea 0%,#764ba2 100%);background:-moz-linear-gradient(315deg,#667eea 0%,#764ba2 100%);background:-o-linear-gradient(315deg,#667eea 0%,#764ba2 100%);background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);cursor:pointer;border:none;-webkit-box-shadow:0 2px 4px rgba(0,0,0,.2);-moz-box-shadow:0 2px 4px rgba(0,0,0,.2);box-shadow:0 2px 4px rgba(0,0,0,.2)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\AudioPlayer.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/AudioPlayer.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/StoryGenerator.tsx":
/*!*******************************************!*\
  !*** ./app/components/StoryGenerator.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StoryGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Loader2_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Volume2,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Volume2,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-2.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Volume2,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction StoryGenerator({ activeTab, currentStory, setCurrentStory, setAudioUrl, isGenerating, setIsGenerating }) {\n    const [storyPrompt, setStoryPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isGeneratingStory, setIsGeneratingStory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const generateStory = async ()=>{\n        if (!storyPrompt.trim()) return;\n        setIsGeneratingStory(true);\n        try {\n            const response = await fetch(\"/api/generate-story\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt: storyPrompt\n                })\n            });\n            if (!response.ok) throw new Error(\"Failed to generate story\");\n            const data = await response.json();\n            setCurrentStory(data.story);\n        } catch (error) {\n            console.error(\"Error generating story:\", error);\n            alert(\"Failed to generate story. Please try again.\");\n        } finally{\n            setIsGeneratingStory(false);\n        }\n    };\n    const convertToSpeech = async ()=>{\n        if (!currentStory.trim()) return;\n        setIsGenerating(true);\n        try {\n            const response = await fetch(\"/api/text-to-speech\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    text: currentStory,\n                    voice: \"af_nicole\",\n                    speed: 0.85\n                })\n            });\n            if (!response.ok) throw new Error(\"Failed to convert to speech\");\n            const data = await response.json();\n            setAudioUrl(data.audioUrl);\n        } catch (error) {\n            console.error(\"Error converting to speech:\", error);\n            alert(\"Failed to convert to speech. Please try again.\");\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            activeTab === \"generate\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-white font-medium mb-2\",\n                                children: \"✨ What kind of story would you like?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: storyPrompt,\n                                    onChange: (e)=>setStoryPrompt(e.target.value),\n                                    placeholder: \"A magical adventure about a brave little rabbit who discovers a hidden forest...\",\n                                    className: \"w-full p-4 rounded-xl border-0 bg-white/10 backdrop-blur-sm text-white placeholder-white/60 resize-none focus:ring-2 focus:ring-white/30 focus:outline-none\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: generateStory,\n                        disabled: !storyPrompt.trim() || isGeneratingStory,\n                        className: \"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 px-6 rounded-xl font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                        children: isGeneratingStory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-5 h-5 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"loading-dots\",\n                                    children: \"Generating story\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generate Story\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-white font-medium mb-2\",\n                        children: \"\\uD83D\\uDCDD Write your own story\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: currentStory,\n                        onChange: (e)=>setCurrentStory(e.target.value),\n                        placeholder: \"Once upon a time, in a magical forest far away...\",\n                        className: \"w-full p-4 rounded-xl border-0 bg-white/10 backdrop-blur-sm text-white placeholder-white/60 resize-none focus:ring-2 focus:ring-white/30 focus:outline-none story-text\",\n                        rows: 8\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this),\n            currentStory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/5 rounded-xl p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white font-medium mb-2 flex items-center gap-2\",\n                                children: \"\\uD83D\\uDCD6 Your Story Preview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/90 story-text max-h-40 overflow-y-auto\",\n                                children: currentStory\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: convertToSpeech,\n                        disabled: isGenerating,\n                        className: \"w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-6 rounded-xl font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-5 h-5 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"loading-dots\",\n                                    children: \"Converting to Nicole's voice\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 17\n                                }, this),\n                                \"Convert to Nicole's Voice\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\components\\\\StoryGenerator.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/StoryGenerator.tsx\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Sparkles_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Sparkles,Volume2,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Sparkles_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Sparkles,Volume2,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Sparkles_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Sparkles,Volume2,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Sparkles_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Sparkles,Volume2,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _components_StoryGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/StoryGenerator */ \"(ssr)/./app/components/StoryGenerator.tsx\");\n/* harmony import */ var _components_AudioPlayer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/AudioPlayer */ \"(ssr)/./app/components/AudioPlayer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [currentStory, setCurrentStory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"generate\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8 pt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-full bg-white/20 backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Sparkles_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-8 h-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-bold text-white\",\n                                    children: \"Nicole Stories\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-white/90 mb-2\",\n                            children: \"\\uD83C\\uDFA7 Beautiful Bedtime Stories with AI\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/70\",\n                            children: \"Generate magical stories and hear them in Nicole's soothing American voice\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-effect rounded-full p-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"generate\"),\n                                    className: `px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center gap-2 ${activeTab === \"generate\" ? \"bg-white text-purple-600 shadow-lg\" : \"text-white hover:bg-white/10\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Sparkles_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Generate Story\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"write\"),\n                                    className: `px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center gap-2 ${activeTab === \"write\" ? \"bg-white text-purple-600 shadow-lg\" : \"text-white hover:bg-white/10\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Sparkles_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Write Story\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-effect rounded-2xl p-6 md:p-8 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StoryGenerator__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        activeTab: activeTab,\n                        currentStory: currentStory,\n                        setCurrentStory: setCurrentStory,\n                        setAudioUrl: setAudioUrl,\n                        isGenerating: isGenerating,\n                        setIsGenerating: setIsGenerating\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-effect rounded-2xl p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioPlayer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        audioUrl: audioUrl\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12 pb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/60 text-sm\",\n                            children: \"Made with ❤️ for beautiful bedtime stories\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-white/70 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Sparkles_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Nicole's Voice\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-white/70 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Sparkles_Volume2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"AI Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f9ea85ccad50\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uaWNvbGUtc3Rvcmllcy1hcHAvLi9hcHAvZ2xvYmFscy5jc3M/OWFkYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY5ZWE4NWNjYWQ1MFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Nicole Stories - AI Bedtime Stories\",\n    description: \"Generate beautiful bedtime stories with Nicole's voice using AI\",\n    keywords: \"bedtime stories, AI stories, text to speech, Nicole voice, children stories\",\n    authors: [\n        {\n            name: \"Nicole Stories App\"\n        }\n    ],\n    openGraph: {\n        title: \"Nicole Stories - AI Bedtime Stories\",\n        description: \"Generate beautiful bedtime stories with Nicole's voice using AI\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen gradient-bg\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kokoro-main\\\\kokoro-main\\\\nicole-stories-app\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\kokoro-main\kokoro-main\nicole-stories-app\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDownloads%5Ckokoro-main%5Ckokoro-main%5Cnicole-stories-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();