#!/usr/bin/env python3
"""
Nicole TTS - Quick Test
اختبار سريع لصوت Nicole
"""

import requests
import webbrowser
import json

def test_nicole_voice():
    print("🎧 اختبار سريع لصوت Nicole...")
    print("=" * 50)
    
    # نص تجريبي قصير
    test_text = "Hello, my name is <PERSON>. I will read you a beautiful bedtime story."
    
    print(f"📝 النص التجريبي: {test_text}")
    print("\n🔄 جاري المحاولة...")
    
    # المواقع للاختبار
    endpoints = [
        {
            'url': 'https://hexgrad-kokoro-tts.hf.space/api/predict',
            'name': 'Hexgrad Kokoro (الأفضل)',
            'payload': {'data': [test_text, 'af_nicole', 0.85, 1.0]}
        },
        {
            'url': 'https://aiqcamp-mcp-kokoro.hf.space/api/predict', 
            'name': 'MCP Kokoro',
            'payload': {'data': [test_text, 'af_nicole', 0.85]}
        }
    ]
    
    for i, endpoint in enumerate(endpoints, 1):
        try:
            print(f"\n🔄 اختبار {i}: {endpoint['name']}")
            
            response = requests.post(
                endpoint['url'],
                json=endpoint['payload'],
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ استجابة ناجحة من {endpoint['name']}")
                
                if result.get('data') and result['data'][0]:
                    audio_data = result['data'][0]
                    audio_url = audio_data.get('url') if isinstance(audio_data, dict) else audio_data
                    
                    if audio_url and audio_url.startswith('/'):
                        base_url = endpoint['url'].replace('/api/predict', '')
                        audio_url = base_url + audio_url
                    
                    if audio_url:
                        print(f"🎵 تم إنتاج الصوت!")
                        print(f"🌐 رابط الصوت: {audio_url}")
                        print(f"🎤 الصوت: Nicole 🇺🇸 🚺")
                        
                        # فتح الصوت في المتصفح
                        print("\n🌐 فتح الصوت في المتصفح...")
                        webbrowser.open(audio_url)
                        
                        print("\n✅ تم! يجب أن تسمع صوت Nicole الآن")
                        print("🎧 إذا لم تسمع شيء، تحقق من مستوى الصوت")
                        
                        return True
                else:
                    print(f"⚠️ لا توجد بيانات صوتية في الاستجابة")
            else:
                print(f"❌ خطأ HTTP: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ انتهت مهلة الاتصال مع {endpoint['name']}")
        except requests.exceptions.ConnectionError:
            print(f"🌐 خطأ في الاتصال مع {endpoint['name']}")
        except Exception as e:
            print(f"❌ خطأ: {str(e)[:50]}...")
    
    print("\n❌ فشل في جميع المحاولات التلقائية")
    print("\n🌐 سأفتح المواقع مباشرة للاستخدام اليدوي...")
    
    # فتح المواقع للاستخدام اليدوي
    sites = [
        'https://huggingface.co/spaces/hexgrad/Kokoro-TTS',
        'https://aiqcamp-mcp-kokoro.hf.space'
    ]
    
    for site in sites:
        webbrowser.open(site)
    
    print("✅ تم فتح المواقع في المتصفح")
    print("\n📋 للاستخدام اليدوي:")
    print("1. الصق هذا النص:")
    print(f"   {test_text}")
    print("2. اختر 'af_nicole' للصوت")
    print("3. اضبط السرعة على 0.85")
    print("4. اضغط Generate/Submit")
    print("5. اضغط Play لسماع الصوت")
    
    return False

if __name__ == "__main__":
    try:
        test_nicole_voice()
    except KeyboardInterrupt:
        print("\n\n👋 تم الإلغاء")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
    
    input("\nاضغط Enter للخروج...")
