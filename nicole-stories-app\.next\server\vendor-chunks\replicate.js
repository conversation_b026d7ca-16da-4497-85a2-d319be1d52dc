/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/replicate";
exports.ids = ["vendor-chunks/replicate"];
exports.modules = {

/***/ "(rsc)/./node_modules/replicate/index.js":
/*!*****************************************!*\
  !*** ./node_modules/replicate/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ApiError = __webpack_require__(/*! ./lib/error */ \"(rsc)/./node_modules/replicate/lib/error.js\");\nconst ModelVersionIdentifier = __webpack_require__(/*! ./lib/identifier */ \"(rsc)/./node_modules/replicate/lib/identifier.js\");\nconst { Stream } = __webpack_require__(/*! ./lib/stream */ \"(rsc)/./node_modules/replicate/lib/stream.js\");\nconst { withAutomaticRetries } = __webpack_require__(/*! ./lib/util */ \"(rsc)/./node_modules/replicate/lib/util.js\");\n\nconst collections = __webpack_require__(/*! ./lib/collections */ \"(rsc)/./node_modules/replicate/lib/collections.js\");\nconst deployments = __webpack_require__(/*! ./lib/deployments */ \"(rsc)/./node_modules/replicate/lib/deployments.js\");\nconst hardware = __webpack_require__(/*! ./lib/hardware */ \"(rsc)/./node_modules/replicate/lib/hardware.js\");\nconst models = __webpack_require__(/*! ./lib/models */ \"(rsc)/./node_modules/replicate/lib/models.js\");\nconst predictions = __webpack_require__(/*! ./lib/predictions */ \"(rsc)/./node_modules/replicate/lib/predictions.js\");\nconst trainings = __webpack_require__(/*! ./lib/trainings */ \"(rsc)/./node_modules/replicate/lib/trainings.js\");\n\nconst packageJSON = __webpack_require__(/*! ./package.json */ \"(rsc)/./node_modules/replicate/package.json\");\n\n/**\n * Replicate API client library\n *\n * @see https://replicate.com/docs/reference/http\n * @example\n * // Create a new Replicate API client instance\n * const Replicate = require(\"replicate\");\n * const replicate = new Replicate({\n *     // get your token from https://replicate.com/account\n *     auth: process.env.REPLICATE_API_TOKEN,\n *     userAgent: \"my-app/1.2.3\"\n * });\n *\n * // Run a model and await the result:\n * const model = 'owner/model:version-id'\n * const input = {text: 'Hello, world!'}\n * const output = await replicate.run(model, { input });\n */\nclass Replicate {\n  /**\n   * Create a new Replicate API client instance.\n   *\n   * @param {object} options - Configuration options for the client\n   * @param {string} options.auth - API access token. Defaults to the `REPLICATE_API_TOKEN` environment variable.\n   * @param {string} options.userAgent - Identifier of your app\n   * @param {string} [options.baseUrl] - Defaults to https://api.replicate.com/v1\n   * @param {Function} [options.fetch] - Fetch function to use. Defaults to `globalThis.fetch`\n   */\n  constructor(options = {}) {\n    this.auth = options.auth || \"****************************************\";\n    this.userAgent =\n      options.userAgent || `replicate-javascript/${packageJSON.version}`;\n    this.baseUrl = options.baseUrl || \"https://api.replicate.com/v1\";\n    this.fetch = options.fetch || globalThis.fetch;\n\n    this.collections = {\n      list: collections.list.bind(this),\n      get: collections.get.bind(this),\n    };\n\n    this.deployments = {\n      predictions: {\n        create: deployments.predictions.create.bind(this),\n      },\n    };\n\n    this.hardware = {\n      list: hardware.list.bind(this),\n    };\n\n    this.models = {\n      get: models.get.bind(this),\n      list: models.list.bind(this),\n      create: models.create.bind(this),\n      versions: {\n        list: models.versions.list.bind(this),\n        get: models.versions.get.bind(this),\n      },\n    };\n\n    this.predictions = {\n      create: predictions.create.bind(this),\n      get: predictions.get.bind(this),\n      cancel: predictions.cancel.bind(this),\n      list: predictions.list.bind(this),\n    };\n\n    this.trainings = {\n      create: trainings.create.bind(this),\n      get: trainings.get.bind(this),\n      cancel: trainings.cancel.bind(this),\n      list: trainings.list.bind(this),\n    };\n  }\n\n  /**\n   * Run a model and wait for its output.\n   *\n   * @param {string} ref - Required. The model version identifier in the format \"owner/name\" or \"owner/name:version\"\n   * @param {object} options\n   * @param {object} options.input - Required. An object with the model inputs\n   * @param {object} [options.wait] - Options for waiting for the prediction to finish\n   * @param {number} [options.wait.interval] - Polling interval in milliseconds. Defaults to 500\n   * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n   * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n   * @param {AbortSignal} [options.signal] - AbortSignal to cancel the prediction\n   * @param {Function} [progress] - Callback function that receives the prediction object as it's updated. The function is called when the prediction is created, each time its updated while polling for completion, and when it's completed.\n   * @throws {Error} If the reference is invalid\n   * @throws {Error} If the prediction failed\n   * @returns {Promise<object>} - Resolves with the output of running the model\n   */\n  async run(ref, options, progress) {\n    const { wait, ...data } = options;\n\n    const identifier = ModelVersionIdentifier.parse(ref);\n\n    let prediction;\n    if (identifier.version) {\n      prediction = await this.predictions.create({\n        ...data,\n        version: identifier.version,\n      });\n    } else if (identifier.owner && identifier.name) {\n      prediction = await this.predictions.create({\n        ...data,\n        model: `${identifier.owner}/${identifier.name}`,\n      });\n    } else {\n      throw new Error(\"Invalid model version identifier\");\n    }\n\n    // Call progress callback with the initial prediction object\n    if (progress) {\n      progress(prediction);\n    }\n\n    const { signal } = options;\n\n    prediction = await this.wait(\n      prediction,\n      wait || {},\n      async (updatedPrediction) => {\n        // Call progress callback with the updated prediction object\n        if (progress) {\n          progress(updatedPrediction);\n        }\n\n        if (signal && signal.aborted) {\n          await this.predictions.cancel(updatedPrediction.id);\n          return true; // stop polling\n        }\n\n        return false; // continue polling\n      }\n    );\n\n    // Call progress callback with the completed prediction object\n    if (progress) {\n      progress(prediction);\n    }\n\n    if (prediction.status === \"failed\") {\n      throw new Error(`Prediction failed: ${prediction.error}`);\n    }\n\n    return prediction.output;\n  }\n\n  /**\n   * Make a request to the Replicate API.\n   *\n   * @param {string} route - REST API endpoint path\n   * @param {object} options - Request parameters\n   * @param {string} [options.method] - HTTP method. Defaults to GET\n   * @param {object} [options.params] - Query parameters\n   * @param {object|Headers} [options.headers] - HTTP headers\n   * @param {object} [options.data] - Body parameters\n   * @returns {Promise<Response>} - Resolves with the response object\n   * @throws {ApiError} If the request failed\n   */\n  async request(route, options) {\n    const { auth, baseUrl, userAgent } = this;\n\n    let url;\n    if (route instanceof URL) {\n      url = route;\n    } else {\n      url = new URL(\n        route.startsWith(\"/\") ? route.slice(1) : route,\n        baseUrl.endsWith(\"/\") ? baseUrl : `${baseUrl}/`\n      );\n    }\n\n    const { method = \"GET\", params = {}, data } = options;\n\n    for (const [key, value] of Object.entries(params)) {\n      url.searchParams.append(key, value);\n    }\n\n    const headers = {};\n    if (auth) {\n      headers[\"Authorization\"] = `Token ${auth}`;\n    }\n    headers[\"Content-Type\"] = \"application/json\";\n    headers[\"User-Agent\"] = userAgent;\n    if (options.headers) {\n      for (const [key, value] of Object.entries(options.headers)) {\n        headers[key] = value;\n      }\n    }\n\n    const init = {\n      method,\n      headers,\n      body: data ? JSON.stringify(data) : undefined,\n    };\n\n    const shouldRetry =\n      method === \"GET\"\n        ? (response) => response.status === 429 || response.status >= 500\n        : (response) => response.status === 429;\n\n    // Workaround to fix `TypeError: Illegal invocation` error in Cloudflare Workers\n    // https://github.com/replicate/replicate-javascript/issues/134\n    const _fetch = this.fetch; // eslint-disable-line no-underscore-dangle\n    const response = await withAutomaticRetries(async () => _fetch(url, init), {\n      shouldRetry,\n    });\n\n    if (!response.ok) {\n      const request = new Request(url, init);\n      const responseText = await response.text();\n      throw new ApiError(\n        `Request to ${url} failed with status ${response.status} ${response.statusText}: ${responseText}.`,\n        request,\n        response\n      );\n    }\n\n    return response;\n  }\n\n  /**\n   * Stream a model and wait for its output.\n   *\n   * @param {string} identifier - Required. The model version identifier in the format \"{owner}/{name}:{version}\"\n   * @param {object} options\n   * @param {object} options.input - Required. An object with the model inputs\n   * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n   * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n   * @param {AbortSignal} [options.signal] - AbortSignal to cancel the prediction\n   * @throws {Error} If the prediction failed\n   * @yields {ServerSentEvent} Each streamed event from the prediction\n   */\n  async *stream(ref, options) {\n    const { wait, ...data } = options;\n\n    const identifier = ModelVersionIdentifier.parse(ref);\n\n    let prediction;\n    if (identifier.version) {\n      prediction = await this.predictions.create({\n        ...data,\n        version: identifier.version,\n        stream: true,\n      });\n    } else if (identifier.owner && identifier.name) {\n      prediction = await this.predictions.create({\n        ...data,\n        model: `${identifier.owner}/${identifier.name}`,\n        stream: true,\n      });\n    } else {\n      throw new Error(\"Invalid model version identifier\");\n    }\n\n    if (prediction.urls && prediction.urls.stream) {\n      const { signal } = options;\n      const stream = new Stream(prediction.urls.stream, { signal });\n      yield* stream;\n    } else {\n      throw new Error(\"Prediction does not support streaming\");\n    }\n  }\n\n  /**\n   * Paginate through a list of results.\n   *\n   * @generator\n   * @example\n   * for await (const page of replicate.paginate(replicate.predictions.list) {\n   *    console.log(page);\n   * }\n   * @param {Function} endpoint - Function that returns a promise for the next page of results\n   * @yields {object[]} Each page of results\n   */\n  async *paginate(endpoint) {\n    const response = await endpoint();\n    yield response.results;\n    if (response.next) {\n      const nextPage = () =>\n        this.request(response.next, { method: \"GET\" }).then((r) => r.json());\n      yield* this.paginate(nextPage);\n    }\n  }\n\n  /**\n   * Wait for a prediction to finish.\n   *\n   * If the prediction has already finished,\n   * this function returns immediately.\n   * Otherwise, it polls the API until the prediction finishes.\n   *\n   * @async\n   * @param {object} prediction - Prediction object\n   * @param {object} options - Options\n   * @param {number} [options.interval] - Polling interval in milliseconds. Defaults to 500\n   * @param {Function} [stop] - Async callback function that is called after each polling attempt. Receives the prediction object as an argument. Return false to cancel polling.\n   * @throws {Error} If the prediction doesn't complete within the maximum number of attempts\n   * @throws {Error} If the prediction failed\n   * @returns {Promise<object>} Resolves with the completed prediction object\n   */\n  async wait(prediction, options, stop) {\n    const { id } = prediction;\n    if (!id) {\n      throw new Error(\"Invalid prediction\");\n    }\n\n    if (\n      prediction.status === \"succeeded\" ||\n      prediction.status === \"failed\" ||\n      prediction.status === \"canceled\"\n    ) {\n      return prediction;\n    }\n\n    // eslint-disable-next-line no-promise-executor-return\n    const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\n\n    const interval = (options && options.interval) || 500;\n\n    let updatedPrediction = await this.predictions.get(id);\n\n    while (\n      updatedPrediction.status !== \"succeeded\" &&\n      updatedPrediction.status !== \"failed\" &&\n      updatedPrediction.status !== \"canceled\"\n    ) {\n      /* eslint-disable no-await-in-loop */\n      if (stop && (await stop(updatedPrediction)) === true) {\n        break;\n      }\n\n      await sleep(interval);\n      updatedPrediction = await this.predictions.get(prediction.id);\n      /* eslint-enable no-await-in-loop */\n    }\n\n    if (updatedPrediction.status === \"failed\") {\n      throw new Error(`Prediction failed: ${updatedPrediction.error}`);\n    }\n\n    return updatedPrediction;\n  }\n}\n\nmodule.exports = Replicate;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLGlCQUFpQixtQkFBTyxDQUFDLGdFQUFhO0FBQ3RDLCtCQUErQixtQkFBTyxDQUFDLDBFQUFrQjtBQUN6RCxRQUFRLFNBQVMsRUFBRSxtQkFBTyxDQUFDLGtFQUFjO0FBQ3pDLFFBQVEsdUJBQXVCLEVBQUUsbUJBQU8sQ0FBQyw4REFBWTs7QUFFckQsb0JBQW9CLG1CQUFPLENBQUMsNEVBQW1CO0FBQy9DLG9CQUFvQixtQkFBTyxDQUFDLDRFQUFtQjtBQUMvQyxpQkFBaUIsbUJBQU8sQ0FBQyxzRUFBZ0I7QUFDekMsZUFBZSxtQkFBTyxDQUFDLGtFQUFjO0FBQ3JDLG9CQUFvQixtQkFBTyxDQUFDLDRFQUFtQjtBQUMvQyxrQkFBa0IsbUJBQU8sQ0FBQyx3RUFBaUI7O0FBRTNDLG9CQUFvQixtQkFBTyxDQUFDLG1FQUFnQjs7QUFFNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEIsK0NBQStDLE9BQU87QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixhQUFhLFFBQVE7QUFDckIsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQixhQUFhLFVBQVU7QUFDdkI7QUFDQSwwQkFBMEI7QUFDMUIsZ0NBQWdDLDBDQUErQjtBQUMvRDtBQUNBLG1EQUFtRCxvQkFBb0I7QUFDdkU7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckIsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQixhQUFhLFFBQVE7QUFDckIsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQixhQUFhLFVBQVU7QUFDdkIsYUFBYSxhQUFhO0FBQzFCLGFBQWEsVUFBVTtBQUN2QixjQUFjLE9BQU87QUFDckIsY0FBYyxPQUFPO0FBQ3JCLGVBQWUsaUJBQWlCO0FBQ2hDO0FBQ0E7QUFDQSxZQUFZLGdCQUFnQjs7QUFFNUI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxNQUFNO0FBQ047QUFDQTtBQUNBLGtCQUFrQixpQkFBaUIsR0FBRyxnQkFBZ0I7QUFDdEQsT0FBTztBQUNQLE1BQU07QUFDTjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFlBQVksU0FBUzs7QUFFckI7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7O0FBRUEsc0JBQXNCO0FBQ3RCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSw0Q0FBNEMsaUJBQWlCO0FBQzdEOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQixhQUFhLFFBQVE7QUFDckIsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsZ0JBQWdCO0FBQzdCLGFBQWEsUUFBUTtBQUNyQixlQUFlLG1CQUFtQjtBQUNsQyxjQUFjLFVBQVU7QUFDeEI7QUFDQTtBQUNBLFlBQVksMkJBQTJCOztBQUV2QztBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLDZDQUE2QyxRQUFRO0FBQ3JEO0FBQ0E7O0FBRUEsWUFBWSwyQkFBMkIsU0FBUzs7QUFFaEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSwwQ0FBMEMsS0FBSztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixLQUFLLHFCQUFxQixpQkFBaUIsRUFBRSxvQkFBb0IsSUFBSSxhQUFhO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRLG9FQUFvRSxNQUFNLEVBQUUsS0FBSyxFQUFFLFFBQVE7QUFDaEgsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQixhQUFhLFFBQVE7QUFDckIsYUFBYSxVQUFVO0FBQ3ZCLGFBQWEsYUFBYTtBQUMxQixjQUFjLE9BQU87QUFDckIsY0FBYyxpQkFBaUI7QUFDL0I7QUFDQTtBQUNBLFlBQVksZ0JBQWdCOztBQUU1Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0E7QUFDQSxrQkFBa0IsaUJBQWlCLEdBQUcsZ0JBQWdCO0FBQ3REO0FBQ0EsT0FBTztBQUNQLE1BQU07QUFDTjtBQUNBOztBQUVBO0FBQ0EsY0FBYyxTQUFTO0FBQ3ZCLDBEQUEwRCxRQUFRO0FBQ2xFO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxVQUFVO0FBQ3ZCLGNBQWMsVUFBVTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsZUFBZTtBQUNyRDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixhQUFhLFFBQVE7QUFDckIsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsVUFBVTtBQUN2QixjQUFjLE9BQU87QUFDckIsY0FBYyxPQUFPO0FBQ3JCLGVBQWUsaUJBQWlCO0FBQ2hDO0FBQ0E7QUFDQSxZQUFZLEtBQUs7QUFDakI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsNENBQTRDLHdCQUF3QjtBQUNwRTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uaWNvbGUtc3Rvcmllcy1hcHAvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2luZGV4LmpzPzNhMGMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgQXBpRXJyb3IgPSByZXF1aXJlKFwiLi9saWIvZXJyb3JcIik7XG5jb25zdCBNb2RlbFZlcnNpb25JZGVudGlmaWVyID0gcmVxdWlyZShcIi4vbGliL2lkZW50aWZpZXJcIik7XG5jb25zdCB7IFN0cmVhbSB9ID0gcmVxdWlyZShcIi4vbGliL3N0cmVhbVwiKTtcbmNvbnN0IHsgd2l0aEF1dG9tYXRpY1JldHJpZXMgfSA9IHJlcXVpcmUoXCIuL2xpYi91dGlsXCIpO1xuXG5jb25zdCBjb2xsZWN0aW9ucyA9IHJlcXVpcmUoXCIuL2xpYi9jb2xsZWN0aW9uc1wiKTtcbmNvbnN0IGRlcGxveW1lbnRzID0gcmVxdWlyZShcIi4vbGliL2RlcGxveW1lbnRzXCIpO1xuY29uc3QgaGFyZHdhcmUgPSByZXF1aXJlKFwiLi9saWIvaGFyZHdhcmVcIik7XG5jb25zdCBtb2RlbHMgPSByZXF1aXJlKFwiLi9saWIvbW9kZWxzXCIpO1xuY29uc3QgcHJlZGljdGlvbnMgPSByZXF1aXJlKFwiLi9saWIvcHJlZGljdGlvbnNcIik7XG5jb25zdCB0cmFpbmluZ3MgPSByZXF1aXJlKFwiLi9saWIvdHJhaW5pbmdzXCIpO1xuXG5jb25zdCBwYWNrYWdlSlNPTiA9IHJlcXVpcmUoXCIuL3BhY2thZ2UuanNvblwiKTtcblxuLyoqXG4gKiBSZXBsaWNhdGUgQVBJIGNsaWVudCBsaWJyYXJ5XG4gKlxuICogQHNlZSBodHRwczovL3JlcGxpY2F0ZS5jb20vZG9jcy9yZWZlcmVuY2UvaHR0cFxuICogQGV4YW1wbGVcbiAqIC8vIENyZWF0ZSBhIG5ldyBSZXBsaWNhdGUgQVBJIGNsaWVudCBpbnN0YW5jZVxuICogY29uc3QgUmVwbGljYXRlID0gcmVxdWlyZShcInJlcGxpY2F0ZVwiKTtcbiAqIGNvbnN0IHJlcGxpY2F0ZSA9IG5ldyBSZXBsaWNhdGUoe1xuICogICAgIC8vIGdldCB5b3VyIHRva2VuIGZyb20gaHR0cHM6Ly9yZXBsaWNhdGUuY29tL2FjY291bnRcbiAqICAgICBhdXRoOiBwcm9jZXNzLmVudi5SRVBMSUNBVEVfQVBJX1RPS0VOLFxuICogICAgIHVzZXJBZ2VudDogXCJteS1hcHAvMS4yLjNcIlxuICogfSk7XG4gKlxuICogLy8gUnVuIGEgbW9kZWwgYW5kIGF3YWl0IHRoZSByZXN1bHQ6XG4gKiBjb25zdCBtb2RlbCA9ICdvd25lci9tb2RlbDp2ZXJzaW9uLWlkJ1xuICogY29uc3QgaW5wdXQgPSB7dGV4dDogJ0hlbGxvLCB3b3JsZCEnfVxuICogY29uc3Qgb3V0cHV0ID0gYXdhaXQgcmVwbGljYXRlLnJ1bihtb2RlbCwgeyBpbnB1dCB9KTtcbiAqL1xuY2xhc3MgUmVwbGljYXRlIHtcbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBSZXBsaWNhdGUgQVBJIGNsaWVudCBpbnN0YW5jZS5cbiAgICpcbiAgICogQHBhcmFtIHtvYmplY3R9IG9wdGlvbnMgLSBDb25maWd1cmF0aW9uIG9wdGlvbnMgZm9yIHRoZSBjbGllbnRcbiAgICogQHBhcmFtIHtzdHJpbmd9IG9wdGlvbnMuYXV0aCAtIEFQSSBhY2Nlc3MgdG9rZW4uIERlZmF1bHRzIHRvIHRoZSBgUkVQTElDQVRFX0FQSV9UT0tFTmAgZW52aXJvbm1lbnQgdmFyaWFibGUuXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBvcHRpb25zLnVzZXJBZ2VudCAtIElkZW50aWZpZXIgb2YgeW91ciBhcHBcbiAgICogQHBhcmFtIHtzdHJpbmd9IFtvcHRpb25zLmJhc2VVcmxdIC0gRGVmYXVsdHMgdG8gaHR0cHM6Ly9hcGkucmVwbGljYXRlLmNvbS92MVxuICAgKiBAcGFyYW0ge0Z1bmN0aW9ufSBbb3B0aW9ucy5mZXRjaF0gLSBGZXRjaCBmdW5jdGlvbiB0byB1c2UuIERlZmF1bHRzIHRvIGBnbG9iYWxUaGlzLmZldGNoYFxuICAgKi9cbiAgY29uc3RydWN0b3Iob3B0aW9ucyA9IHt9KSB7XG4gICAgdGhpcy5hdXRoID0gb3B0aW9ucy5hdXRoIHx8IHByb2Nlc3MuZW52LlJFUExJQ0FURV9BUElfVE9LRU47XG4gICAgdGhpcy51c2VyQWdlbnQgPVxuICAgICAgb3B0aW9ucy51c2VyQWdlbnQgfHwgYHJlcGxpY2F0ZS1qYXZhc2NyaXB0LyR7cGFja2FnZUpTT04udmVyc2lvbn1gO1xuICAgIHRoaXMuYmFzZVVybCA9IG9wdGlvbnMuYmFzZVVybCB8fCBcImh0dHBzOi8vYXBpLnJlcGxpY2F0ZS5jb20vdjFcIjtcbiAgICB0aGlzLmZldGNoID0gb3B0aW9ucy5mZXRjaCB8fCBnbG9iYWxUaGlzLmZldGNoO1xuXG4gICAgdGhpcy5jb2xsZWN0aW9ucyA9IHtcbiAgICAgIGxpc3Q6IGNvbGxlY3Rpb25zLmxpc3QuYmluZCh0aGlzKSxcbiAgICAgIGdldDogY29sbGVjdGlvbnMuZ2V0LmJpbmQodGhpcyksXG4gICAgfTtcblxuICAgIHRoaXMuZGVwbG95bWVudHMgPSB7XG4gICAgICBwcmVkaWN0aW9uczoge1xuICAgICAgICBjcmVhdGU6IGRlcGxveW1lbnRzLnByZWRpY3Rpb25zLmNyZWF0ZS5iaW5kKHRoaXMpLFxuICAgICAgfSxcbiAgICB9O1xuXG4gICAgdGhpcy5oYXJkd2FyZSA9IHtcbiAgICAgIGxpc3Q6IGhhcmR3YXJlLmxpc3QuYmluZCh0aGlzKSxcbiAgICB9O1xuXG4gICAgdGhpcy5tb2RlbHMgPSB7XG4gICAgICBnZXQ6IG1vZGVscy5nZXQuYmluZCh0aGlzKSxcbiAgICAgIGxpc3Q6IG1vZGVscy5saXN0LmJpbmQodGhpcyksXG4gICAgICBjcmVhdGU6IG1vZGVscy5jcmVhdGUuYmluZCh0aGlzKSxcbiAgICAgIHZlcnNpb25zOiB7XG4gICAgICAgIGxpc3Q6IG1vZGVscy52ZXJzaW9ucy5saXN0LmJpbmQodGhpcyksXG4gICAgICAgIGdldDogbW9kZWxzLnZlcnNpb25zLmdldC5iaW5kKHRoaXMpLFxuICAgICAgfSxcbiAgICB9O1xuXG4gICAgdGhpcy5wcmVkaWN0aW9ucyA9IHtcbiAgICAgIGNyZWF0ZTogcHJlZGljdGlvbnMuY3JlYXRlLmJpbmQodGhpcyksXG4gICAgICBnZXQ6IHByZWRpY3Rpb25zLmdldC5iaW5kKHRoaXMpLFxuICAgICAgY2FuY2VsOiBwcmVkaWN0aW9ucy5jYW5jZWwuYmluZCh0aGlzKSxcbiAgICAgIGxpc3Q6IHByZWRpY3Rpb25zLmxpc3QuYmluZCh0aGlzKSxcbiAgICB9O1xuXG4gICAgdGhpcy50cmFpbmluZ3MgPSB7XG4gICAgICBjcmVhdGU6IHRyYWluaW5ncy5jcmVhdGUuYmluZCh0aGlzKSxcbiAgICAgIGdldDogdHJhaW5pbmdzLmdldC5iaW5kKHRoaXMpLFxuICAgICAgY2FuY2VsOiB0cmFpbmluZ3MuY2FuY2VsLmJpbmQodGhpcyksXG4gICAgICBsaXN0OiB0cmFpbmluZ3MubGlzdC5iaW5kKHRoaXMpLFxuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogUnVuIGEgbW9kZWwgYW5kIHdhaXQgZm9yIGl0cyBvdXRwdXQuXG4gICAqXG4gICAqIEBwYXJhbSB7c3RyaW5nfSByZWYgLSBSZXF1aXJlZC4gVGhlIG1vZGVsIHZlcnNpb24gaWRlbnRpZmllciBpbiB0aGUgZm9ybWF0IFwib3duZXIvbmFtZVwiIG9yIFwib3duZXIvbmFtZTp2ZXJzaW9uXCJcbiAgICogQHBhcmFtIHtvYmplY3R9IG9wdGlvbnNcbiAgICogQHBhcmFtIHtvYmplY3R9IG9wdGlvbnMuaW5wdXQgLSBSZXF1aXJlZC4gQW4gb2JqZWN0IHdpdGggdGhlIG1vZGVsIGlucHV0c1xuICAgKiBAcGFyYW0ge29iamVjdH0gW29wdGlvbnMud2FpdF0gLSBPcHRpb25zIGZvciB3YWl0aW5nIGZvciB0aGUgcHJlZGljdGlvbiB0byBmaW5pc2hcbiAgICogQHBhcmFtIHtudW1iZXJ9IFtvcHRpb25zLndhaXQuaW50ZXJ2YWxdIC0gUG9sbGluZyBpbnRlcnZhbCBpbiBtaWxsaXNlY29uZHMuIERlZmF1bHRzIHRvIDUwMFxuICAgKiBAcGFyYW0ge3N0cmluZ30gW29wdGlvbnMud2ViaG9va10gLSBBbiBIVFRQUyBVUkwgZm9yIHJlY2VpdmluZyBhIHdlYmhvb2sgd2hlbiB0aGUgcHJlZGljdGlvbiBoYXMgbmV3IG91dHB1dFxuICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBbb3B0aW9ucy53ZWJob29rX2V2ZW50c19maWx0ZXJdIC0gWW91IGNhbiBjaGFuZ2Ugd2hpY2ggZXZlbnRzIHRyaWdnZXIgd2ViaG9vayByZXF1ZXN0cyBieSBzcGVjaWZ5aW5nIHdlYmhvb2sgZXZlbnRzIChgc3RhcnRgfGBvdXRwdXRgfGBsb2dzYHxgY29tcGxldGVkYClcbiAgICogQHBhcmFtIHtBYm9ydFNpZ25hbH0gW29wdGlvbnMuc2lnbmFsXSAtIEFib3J0U2lnbmFsIHRvIGNhbmNlbCB0aGUgcHJlZGljdGlvblxuICAgKiBAcGFyYW0ge0Z1bmN0aW9ufSBbcHJvZ3Jlc3NdIC0gQ2FsbGJhY2sgZnVuY3Rpb24gdGhhdCByZWNlaXZlcyB0aGUgcHJlZGljdGlvbiBvYmplY3QgYXMgaXQncyB1cGRhdGVkLiBUaGUgZnVuY3Rpb24gaXMgY2FsbGVkIHdoZW4gdGhlIHByZWRpY3Rpb24gaXMgY3JlYXRlZCwgZWFjaCB0aW1lIGl0cyB1cGRhdGVkIHdoaWxlIHBvbGxpbmcgZm9yIGNvbXBsZXRpb24sIGFuZCB3aGVuIGl0J3MgY29tcGxldGVkLlxuICAgKiBAdGhyb3dzIHtFcnJvcn0gSWYgdGhlIHJlZmVyZW5jZSBpcyBpbnZhbGlkXG4gICAqIEB0aHJvd3Mge0Vycm9yfSBJZiB0aGUgcHJlZGljdGlvbiBmYWlsZWRcbiAgICogQHJldHVybnMge1Byb21pc2U8b2JqZWN0Pn0gLSBSZXNvbHZlcyB3aXRoIHRoZSBvdXRwdXQgb2YgcnVubmluZyB0aGUgbW9kZWxcbiAgICovXG4gIGFzeW5jIHJ1bihyZWYsIG9wdGlvbnMsIHByb2dyZXNzKSB7XG4gICAgY29uc3QgeyB3YWl0LCAuLi5kYXRhIH0gPSBvcHRpb25zO1xuXG4gICAgY29uc3QgaWRlbnRpZmllciA9IE1vZGVsVmVyc2lvbklkZW50aWZpZXIucGFyc2UocmVmKTtcblxuICAgIGxldCBwcmVkaWN0aW9uO1xuICAgIGlmIChpZGVudGlmaWVyLnZlcnNpb24pIHtcbiAgICAgIHByZWRpY3Rpb24gPSBhd2FpdCB0aGlzLnByZWRpY3Rpb25zLmNyZWF0ZSh7XG4gICAgICAgIC4uLmRhdGEsXG4gICAgICAgIHZlcnNpb246IGlkZW50aWZpZXIudmVyc2lvbixcbiAgICAgIH0pO1xuICAgIH0gZWxzZSBpZiAoaWRlbnRpZmllci5vd25lciAmJiBpZGVudGlmaWVyLm5hbWUpIHtcbiAgICAgIHByZWRpY3Rpb24gPSBhd2FpdCB0aGlzLnByZWRpY3Rpb25zLmNyZWF0ZSh7XG4gICAgICAgIC4uLmRhdGEsXG4gICAgICAgIG1vZGVsOiBgJHtpZGVudGlmaWVyLm93bmVyfS8ke2lkZW50aWZpZXIubmFtZX1gLFxuICAgICAgfSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgbW9kZWwgdmVyc2lvbiBpZGVudGlmaWVyXCIpO1xuICAgIH1cblxuICAgIC8vIENhbGwgcHJvZ3Jlc3MgY2FsbGJhY2sgd2l0aCB0aGUgaW5pdGlhbCBwcmVkaWN0aW9uIG9iamVjdFxuICAgIGlmIChwcm9ncmVzcykge1xuICAgICAgcHJvZ3Jlc3MocHJlZGljdGlvbik7XG4gICAgfVxuXG4gICAgY29uc3QgeyBzaWduYWwgfSA9IG9wdGlvbnM7XG5cbiAgICBwcmVkaWN0aW9uID0gYXdhaXQgdGhpcy53YWl0KFxuICAgICAgcHJlZGljdGlvbixcbiAgICAgIHdhaXQgfHwge30sXG4gICAgICBhc3luYyAodXBkYXRlZFByZWRpY3Rpb24pID0+IHtcbiAgICAgICAgLy8gQ2FsbCBwcm9ncmVzcyBjYWxsYmFjayB3aXRoIHRoZSB1cGRhdGVkIHByZWRpY3Rpb24gb2JqZWN0XG4gICAgICAgIGlmIChwcm9ncmVzcykge1xuICAgICAgICAgIHByb2dyZXNzKHVwZGF0ZWRQcmVkaWN0aW9uKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChzaWduYWwgJiYgc2lnbmFsLmFib3J0ZWQpIHtcbiAgICAgICAgICBhd2FpdCB0aGlzLnByZWRpY3Rpb25zLmNhbmNlbCh1cGRhdGVkUHJlZGljdGlvbi5pZCk7XG4gICAgICAgICAgcmV0dXJuIHRydWU7IC8vIHN0b3AgcG9sbGluZ1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGZhbHNlOyAvLyBjb250aW51ZSBwb2xsaW5nXG4gICAgICB9XG4gICAgKTtcblxuICAgIC8vIENhbGwgcHJvZ3Jlc3MgY2FsbGJhY2sgd2l0aCB0aGUgY29tcGxldGVkIHByZWRpY3Rpb24gb2JqZWN0XG4gICAgaWYgKHByb2dyZXNzKSB7XG4gICAgICBwcm9ncmVzcyhwcmVkaWN0aW9uKTtcbiAgICB9XG5cbiAgICBpZiAocHJlZGljdGlvbi5zdGF0dXMgPT09IFwiZmFpbGVkXCIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgUHJlZGljdGlvbiBmYWlsZWQ6ICR7cHJlZGljdGlvbi5lcnJvcn1gKTtcbiAgICB9XG5cbiAgICByZXR1cm4gcHJlZGljdGlvbi5vdXRwdXQ7XG4gIH1cblxuICAvKipcbiAgICogTWFrZSBhIHJlcXVlc3QgdG8gdGhlIFJlcGxpY2F0ZSBBUEkuXG4gICAqXG4gICAqIEBwYXJhbSB7c3RyaW5nfSByb3V0ZSAtIFJFU1QgQVBJIGVuZHBvaW50IHBhdGhcbiAgICogQHBhcmFtIHtvYmplY3R9IG9wdGlvbnMgLSBSZXF1ZXN0IHBhcmFtZXRlcnNcbiAgICogQHBhcmFtIHtzdHJpbmd9IFtvcHRpb25zLm1ldGhvZF0gLSBIVFRQIG1ldGhvZC4gRGVmYXVsdHMgdG8gR0VUXG4gICAqIEBwYXJhbSB7b2JqZWN0fSBbb3B0aW9ucy5wYXJhbXNdIC0gUXVlcnkgcGFyYW1ldGVyc1xuICAgKiBAcGFyYW0ge29iamVjdHxIZWFkZXJzfSBbb3B0aW9ucy5oZWFkZXJzXSAtIEhUVFAgaGVhZGVyc1xuICAgKiBAcGFyYW0ge29iamVjdH0gW29wdGlvbnMuZGF0YV0gLSBCb2R5IHBhcmFtZXRlcnNcbiAgICogQHJldHVybnMge1Byb21pc2U8UmVzcG9uc2U+fSAtIFJlc29sdmVzIHdpdGggdGhlIHJlc3BvbnNlIG9iamVjdFxuICAgKiBAdGhyb3dzIHtBcGlFcnJvcn0gSWYgdGhlIHJlcXVlc3QgZmFpbGVkXG4gICAqL1xuICBhc3luYyByZXF1ZXN0KHJvdXRlLCBvcHRpb25zKSB7XG4gICAgY29uc3QgeyBhdXRoLCBiYXNlVXJsLCB1c2VyQWdlbnQgfSA9IHRoaXM7XG5cbiAgICBsZXQgdXJsO1xuICAgIGlmIChyb3V0ZSBpbnN0YW5jZW9mIFVSTCkge1xuICAgICAgdXJsID0gcm91dGU7XG4gICAgfSBlbHNlIHtcbiAgICAgIHVybCA9IG5ldyBVUkwoXG4gICAgICAgIHJvdXRlLnN0YXJ0c1dpdGgoXCIvXCIpID8gcm91dGUuc2xpY2UoMSkgOiByb3V0ZSxcbiAgICAgICAgYmFzZVVybC5lbmRzV2l0aChcIi9cIikgPyBiYXNlVXJsIDogYCR7YmFzZVVybH0vYFxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCB7IG1ldGhvZCA9IFwiR0VUXCIsIHBhcmFtcyA9IHt9LCBkYXRhIH0gPSBvcHRpb25zO1xuXG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMocGFyYW1zKSkge1xuICAgICAgdXJsLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgfVxuXG4gICAgY29uc3QgaGVhZGVycyA9IHt9O1xuICAgIGlmIChhdXRoKSB7XG4gICAgICBoZWFkZXJzW1wiQXV0aG9yaXphdGlvblwiXSA9IGBUb2tlbiAke2F1dGh9YDtcbiAgICB9XG4gICAgaGVhZGVyc1tcIkNvbnRlbnQtVHlwZVwiXSA9IFwiYXBwbGljYXRpb24vanNvblwiO1xuICAgIGhlYWRlcnNbXCJVc2VyLUFnZW50XCJdID0gdXNlckFnZW50O1xuICAgIGlmIChvcHRpb25zLmhlYWRlcnMpIHtcbiAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKG9wdGlvbnMuaGVhZGVycykpIHtcbiAgICAgICAgaGVhZGVyc1trZXldID0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgaW5pdCA9IHtcbiAgICAgIG1ldGhvZCxcbiAgICAgIGhlYWRlcnMsXG4gICAgICBib2R5OiBkYXRhID8gSlNPTi5zdHJpbmdpZnkoZGF0YSkgOiB1bmRlZmluZWQsXG4gICAgfTtcblxuICAgIGNvbnN0IHNob3VsZFJldHJ5ID1cbiAgICAgIG1ldGhvZCA9PT0gXCJHRVRcIlxuICAgICAgICA/IChyZXNwb25zZSkgPT4gcmVzcG9uc2Uuc3RhdHVzID09PSA0MjkgfHwgcmVzcG9uc2Uuc3RhdHVzID49IDUwMFxuICAgICAgICA6IChyZXNwb25zZSkgPT4gcmVzcG9uc2Uuc3RhdHVzID09PSA0Mjk7XG5cbiAgICAvLyBXb3JrYXJvdW5kIHRvIGZpeCBgVHlwZUVycm9yOiBJbGxlZ2FsIGludm9jYXRpb25gIGVycm9yIGluIENsb3VkZmxhcmUgV29ya2Vyc1xuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9yZXBsaWNhdGUvcmVwbGljYXRlLWphdmFzY3JpcHQvaXNzdWVzLzEzNFxuICAgIGNvbnN0IF9mZXRjaCA9IHRoaXMuZmV0Y2g7IC8vIGVzbGludC1kaXNhYmxlLWxpbmUgbm8tdW5kZXJzY29yZS1kYW5nbGVcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHdpdGhBdXRvbWF0aWNSZXRyaWVzKGFzeW5jICgpID0+IF9mZXRjaCh1cmwsIGluaXQpLCB7XG4gICAgICBzaG91bGRSZXRyeSxcbiAgICB9KTtcblxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnN0IHJlcXVlc3QgPSBuZXcgUmVxdWVzdCh1cmwsIGluaXQpO1xuICAgICAgY29uc3QgcmVzcG9uc2VUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgdGhyb3cgbmV3IEFwaUVycm9yKFxuICAgICAgICBgUmVxdWVzdCB0byAke3VybH0gZmFpbGVkIHdpdGggc3RhdHVzICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9OiAke3Jlc3BvbnNlVGV4dH0uYCxcbiAgICAgICAgcmVxdWVzdCxcbiAgICAgICAgcmVzcG9uc2VcbiAgICAgICk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9XG5cbiAgLyoqXG4gICAqIFN0cmVhbSBhIG1vZGVsIGFuZCB3YWl0IGZvciBpdHMgb3V0cHV0LlxuICAgKlxuICAgKiBAcGFyYW0ge3N0cmluZ30gaWRlbnRpZmllciAtIFJlcXVpcmVkLiBUaGUgbW9kZWwgdmVyc2lvbiBpZGVudGlmaWVyIGluIHRoZSBmb3JtYXQgXCJ7b3duZXJ9L3tuYW1lfTp7dmVyc2lvbn1cIlxuICAgKiBAcGFyYW0ge29iamVjdH0gb3B0aW9uc1xuICAgKiBAcGFyYW0ge29iamVjdH0gb3B0aW9ucy5pbnB1dCAtIFJlcXVpcmVkLiBBbiBvYmplY3Qgd2l0aCB0aGUgbW9kZWwgaW5wdXRzXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBbb3B0aW9ucy53ZWJob29rXSAtIEFuIEhUVFBTIFVSTCBmb3IgcmVjZWl2aW5nIGEgd2ViaG9vayB3aGVuIHRoZSBwcmVkaWN0aW9uIGhhcyBuZXcgb3V0cHV0XG4gICAqIEBwYXJhbSB7c3RyaW5nW119IFtvcHRpb25zLndlYmhvb2tfZXZlbnRzX2ZpbHRlcl0gLSBZb3UgY2FuIGNoYW5nZSB3aGljaCBldmVudHMgdHJpZ2dlciB3ZWJob29rIHJlcXVlc3RzIGJ5IHNwZWNpZnlpbmcgd2ViaG9vayBldmVudHMgKGBzdGFydGB8YG91dHB1dGB8YGxvZ3NgfGBjb21wbGV0ZWRgKVxuICAgKiBAcGFyYW0ge0Fib3J0U2lnbmFsfSBbb3B0aW9ucy5zaWduYWxdIC0gQWJvcnRTaWduYWwgdG8gY2FuY2VsIHRoZSBwcmVkaWN0aW9uXG4gICAqIEB0aHJvd3Mge0Vycm9yfSBJZiB0aGUgcHJlZGljdGlvbiBmYWlsZWRcbiAgICogQHlpZWxkcyB7U2VydmVyU2VudEV2ZW50fSBFYWNoIHN0cmVhbWVkIGV2ZW50IGZyb20gdGhlIHByZWRpY3Rpb25cbiAgICovXG4gIGFzeW5jICpzdHJlYW0ocmVmLCBvcHRpb25zKSB7XG4gICAgY29uc3QgeyB3YWl0LCAuLi5kYXRhIH0gPSBvcHRpb25zO1xuXG4gICAgY29uc3QgaWRlbnRpZmllciA9IE1vZGVsVmVyc2lvbklkZW50aWZpZXIucGFyc2UocmVmKTtcblxuICAgIGxldCBwcmVkaWN0aW9uO1xuICAgIGlmIChpZGVudGlmaWVyLnZlcnNpb24pIHtcbiAgICAgIHByZWRpY3Rpb24gPSBhd2FpdCB0aGlzLnByZWRpY3Rpb25zLmNyZWF0ZSh7XG4gICAgICAgIC4uLmRhdGEsXG4gICAgICAgIHZlcnNpb246IGlkZW50aWZpZXIudmVyc2lvbixcbiAgICAgICAgc3RyZWFtOiB0cnVlLFxuICAgICAgfSk7XG4gICAgfSBlbHNlIGlmIChpZGVudGlmaWVyLm93bmVyICYmIGlkZW50aWZpZXIubmFtZSkge1xuICAgICAgcHJlZGljdGlvbiA9IGF3YWl0IHRoaXMucHJlZGljdGlvbnMuY3JlYXRlKHtcbiAgICAgICAgLi4uZGF0YSxcbiAgICAgICAgbW9kZWw6IGAke2lkZW50aWZpZXIub3duZXJ9LyR7aWRlbnRpZmllci5uYW1lfWAsXG4gICAgICAgIHN0cmVhbTogdHJ1ZSxcbiAgICAgIH0pO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIG1vZGVsIHZlcnNpb24gaWRlbnRpZmllclwiKTtcbiAgICB9XG5cbiAgICBpZiAocHJlZGljdGlvbi51cmxzICYmIHByZWRpY3Rpb24udXJscy5zdHJlYW0pIHtcbiAgICAgIGNvbnN0IHsgc2lnbmFsIH0gPSBvcHRpb25zO1xuICAgICAgY29uc3Qgc3RyZWFtID0gbmV3IFN0cmVhbShwcmVkaWN0aW9uLnVybHMuc3RyZWFtLCB7IHNpZ25hbCB9KTtcbiAgICAgIHlpZWxkKiBzdHJlYW07XG4gICAgfSBlbHNlIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcIlByZWRpY3Rpb24gZG9lcyBub3Qgc3VwcG9ydCBzdHJlYW1pbmdcIik7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFBhZ2luYXRlIHRocm91Z2ggYSBsaXN0IG9mIHJlc3VsdHMuXG4gICAqXG4gICAqIEBnZW5lcmF0b3JcbiAgICogQGV4YW1wbGVcbiAgICogZm9yIGF3YWl0IChjb25zdCBwYWdlIG9mIHJlcGxpY2F0ZS5wYWdpbmF0ZShyZXBsaWNhdGUucHJlZGljdGlvbnMubGlzdCkge1xuICAgKiAgICBjb25zb2xlLmxvZyhwYWdlKTtcbiAgICogfVxuICAgKiBAcGFyYW0ge0Z1bmN0aW9ufSBlbmRwb2ludCAtIEZ1bmN0aW9uIHRoYXQgcmV0dXJucyBhIHByb21pc2UgZm9yIHRoZSBuZXh0IHBhZ2Ugb2YgcmVzdWx0c1xuICAgKiBAeWllbGRzIHtvYmplY3RbXX0gRWFjaCBwYWdlIG9mIHJlc3VsdHNcbiAgICovXG4gIGFzeW5jICpwYWdpbmF0ZShlbmRwb2ludCkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZW5kcG9pbnQoKTtcbiAgICB5aWVsZCByZXNwb25zZS5yZXN1bHRzO1xuICAgIGlmIChyZXNwb25zZS5uZXh0KSB7XG4gICAgICBjb25zdCBuZXh0UGFnZSA9ICgpID0+XG4gICAgICAgIHRoaXMucmVxdWVzdChyZXNwb25zZS5uZXh0LCB7IG1ldGhvZDogXCJHRVRcIiB9KS50aGVuKChyKSA9PiByLmpzb24oKSk7XG4gICAgICB5aWVsZCogdGhpcy5wYWdpbmF0ZShuZXh0UGFnZSk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFdhaXQgZm9yIGEgcHJlZGljdGlvbiB0byBmaW5pc2guXG4gICAqXG4gICAqIElmIHRoZSBwcmVkaWN0aW9uIGhhcyBhbHJlYWR5IGZpbmlzaGVkLFxuICAgKiB0aGlzIGZ1bmN0aW9uIHJldHVybnMgaW1tZWRpYXRlbHkuXG4gICAqIE90aGVyd2lzZSwgaXQgcG9sbHMgdGhlIEFQSSB1bnRpbCB0aGUgcHJlZGljdGlvbiBmaW5pc2hlcy5cbiAgICpcbiAgICogQGFzeW5jXG4gICAqIEBwYXJhbSB7b2JqZWN0fSBwcmVkaWN0aW9uIC0gUHJlZGljdGlvbiBvYmplY3RcbiAgICogQHBhcmFtIHtvYmplY3R9IG9wdGlvbnMgLSBPcHRpb25zXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBbb3B0aW9ucy5pbnRlcnZhbF0gLSBQb2xsaW5nIGludGVydmFsIGluIG1pbGxpc2Vjb25kcy4gRGVmYXVsdHMgdG8gNTAwXG4gICAqIEBwYXJhbSB7RnVuY3Rpb259IFtzdG9wXSAtIEFzeW5jIGNhbGxiYWNrIGZ1bmN0aW9uIHRoYXQgaXMgY2FsbGVkIGFmdGVyIGVhY2ggcG9sbGluZyBhdHRlbXB0LiBSZWNlaXZlcyB0aGUgcHJlZGljdGlvbiBvYmplY3QgYXMgYW4gYXJndW1lbnQuIFJldHVybiBmYWxzZSB0byBjYW5jZWwgcG9sbGluZy5cbiAgICogQHRocm93cyB7RXJyb3J9IElmIHRoZSBwcmVkaWN0aW9uIGRvZXNuJ3QgY29tcGxldGUgd2l0aGluIHRoZSBtYXhpbXVtIG51bWJlciBvZiBhdHRlbXB0c1xuICAgKiBAdGhyb3dzIHtFcnJvcn0gSWYgdGhlIHByZWRpY3Rpb24gZmFpbGVkXG4gICAqIEByZXR1cm5zIHtQcm9taXNlPG9iamVjdD59IFJlc29sdmVzIHdpdGggdGhlIGNvbXBsZXRlZCBwcmVkaWN0aW9uIG9iamVjdFxuICAgKi9cbiAgYXN5bmMgd2FpdChwcmVkaWN0aW9uLCBvcHRpb25zLCBzdG9wKSB7XG4gICAgY29uc3QgeyBpZCB9ID0gcHJlZGljdGlvbjtcbiAgICBpZiAoIWlkKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIHByZWRpY3Rpb25cIik7XG4gICAgfVxuXG4gICAgaWYgKFxuICAgICAgcHJlZGljdGlvbi5zdGF0dXMgPT09IFwic3VjY2VlZGVkXCIgfHxcbiAgICAgIHByZWRpY3Rpb24uc3RhdHVzID09PSBcImZhaWxlZFwiIHx8XG4gICAgICBwcmVkaWN0aW9uLnN0YXR1cyA9PT0gXCJjYW5jZWxlZFwiXG4gICAgKSB7XG4gICAgICByZXR1cm4gcHJlZGljdGlvbjtcbiAgICB9XG5cbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tcHJvbWlzZS1leGVjdXRvci1yZXR1cm5cbiAgICBjb25zdCBzbGVlcCA9IChtcykgPT4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgbXMpKTtcblxuICAgIGNvbnN0IGludGVydmFsID0gKG9wdGlvbnMgJiYgb3B0aW9ucy5pbnRlcnZhbCkgfHwgNTAwO1xuXG4gICAgbGV0IHVwZGF0ZWRQcmVkaWN0aW9uID0gYXdhaXQgdGhpcy5wcmVkaWN0aW9ucy5nZXQoaWQpO1xuXG4gICAgd2hpbGUgKFxuICAgICAgdXBkYXRlZFByZWRpY3Rpb24uc3RhdHVzICE9PSBcInN1Y2NlZWRlZFwiICYmXG4gICAgICB1cGRhdGVkUHJlZGljdGlvbi5zdGF0dXMgIT09IFwiZmFpbGVkXCIgJiZcbiAgICAgIHVwZGF0ZWRQcmVkaWN0aW9uLnN0YXR1cyAhPT0gXCJjYW5jZWxlZFwiXG4gICAgKSB7XG4gICAgICAvKiBlc2xpbnQtZGlzYWJsZSBuby1hd2FpdC1pbi1sb29wICovXG4gICAgICBpZiAoc3RvcCAmJiAoYXdhaXQgc3RvcCh1cGRhdGVkUHJlZGljdGlvbikpID09PSB0cnVlKSB7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuXG4gICAgICBhd2FpdCBzbGVlcChpbnRlcnZhbCk7XG4gICAgICB1cGRhdGVkUHJlZGljdGlvbiA9IGF3YWl0IHRoaXMucHJlZGljdGlvbnMuZ2V0KHByZWRpY3Rpb24uaWQpO1xuICAgICAgLyogZXNsaW50LWVuYWJsZSBuby1hd2FpdC1pbi1sb29wICovXG4gICAgfVxuXG4gICAgaWYgKHVwZGF0ZWRQcmVkaWN0aW9uLnN0YXR1cyA9PT0gXCJmYWlsZWRcIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBQcmVkaWN0aW9uIGZhaWxlZDogJHt1cGRhdGVkUHJlZGljdGlvbi5lcnJvcn1gKTtcbiAgICB9XG5cbiAgICByZXR1cm4gdXBkYXRlZFByZWRpY3Rpb247XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBSZXBsaWNhdGU7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/collections.js":
/*!***************************************************!*\
  !*** ./node_modules/replicate/lib/collections.js ***!
  \***************************************************/
/***/ ((module) => {

eval("/**\n * Fetch a model collection\n *\n * @param {string} collection_slug - Required. The slug of the collection. See http://replicate.com/collections\n * @returns {Promise<object>} - Resolves with the collection data\n */\nasync function getCollection(collection_slug) {\n  const response = await this.request(`/collections/${collection_slug}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Fetch a list of model collections\n *\n * @returns {Promise<object>} - Resolves with the collections data\n */\nasync function listCollections() {\n  const response = await this.request(\"/collections\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = { get: getCollection, list: listCollections };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi9jb2xsZWN0aW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYSxpQkFBaUI7QUFDOUI7QUFDQTtBQUNBLHNEQUFzRCxnQkFBZ0I7QUFDdEU7QUFDQSxHQUFHOztBQUVIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYSxpQkFBaUI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7O0FBRUEsbUJBQW1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmljb2xlLXN0b3JpZXMtYXBwLy4vbm9kZV9tb2R1bGVzL3JlcGxpY2F0ZS9saWIvY29sbGVjdGlvbnMuanM/ZjIyMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEZldGNoIGEgbW9kZWwgY29sbGVjdGlvblxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBjb2xsZWN0aW9uX3NsdWcgLSBSZXF1aXJlZC4gVGhlIHNsdWcgb2YgdGhlIGNvbGxlY3Rpb24uIFNlZSBodHRwOi8vcmVwbGljYXRlLmNvbS9jb2xsZWN0aW9uc1xuICogQHJldHVybnMge1Byb21pc2U8b2JqZWN0Pn0gLSBSZXNvbHZlcyB3aXRoIHRoZSBjb2xsZWN0aW9uIGRhdGFcbiAqL1xuYXN5bmMgZnVuY3Rpb24gZ2V0Q29sbGVjdGlvbihjb2xsZWN0aW9uX3NsdWcpIHtcbiAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLnJlcXVlc3QoYC9jb2xsZWN0aW9ucy8ke2NvbGxlY3Rpb25fc2x1Z31gLCB7XG4gICAgbWV0aG9kOiBcIkdFVFwiLFxuICB9KTtcblxuICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xufVxuXG4vKipcbiAqIEZldGNoIGEgbGlzdCBvZiBtb2RlbCBjb2xsZWN0aW9uc1xuICpcbiAqIEByZXR1cm5zIHtQcm9taXNlPG9iamVjdD59IC0gUmVzb2x2ZXMgd2l0aCB0aGUgY29sbGVjdGlvbnMgZGF0YVxuICovXG5hc3luYyBmdW5jdGlvbiBsaXN0Q29sbGVjdGlvbnMoKSB7XG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5yZXF1ZXN0KFwiL2NvbGxlY3Rpb25zXCIsIHtcbiAgICBtZXRob2Q6IFwiR0VUXCIsXG4gIH0pO1xuXG4gIHJldHVybiByZXNwb25zZS5qc29uKCk7XG59XG5cbm1vZHVsZS5leHBvcnRzID0geyBnZXQ6IGdldENvbGxlY3Rpb24sIGxpc3Q6IGxpc3RDb2xsZWN0aW9ucyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/collections.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/deployments.js":
/*!***************************************************!*\
  !*** ./node_modules/replicate/lib/deployments.js ***!
  \***************************************************/
/***/ ((module) => {

eval("/**\n * Create a new prediction with a deployment\n *\n * @param {string} deployment_owner - Required. The username of the user or organization who owns the deployment\n * @param {string} deployment_name - Required. The name of the deployment\n * @param {object} options\n * @param {object} options.input - Required. An object with the model inputs\n * @param {boolean} [options.stream] - Whether to stream the prediction output. Defaults to false\n * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n * @returns {Promise<object>} Resolves with the created prediction data\n */\nasync function createPrediction(deployment_owner, deployment_name, options) {\n  const { stream, ...data } = options;\n\n  if (data.webhook) {\n    try {\n      // eslint-disable-next-line no-new\n      new URL(data.webhook);\n    } catch (err) {\n      throw new Error(\"Invalid webhook URL\");\n    }\n  }\n\n  const response = await this.request(\n    `/deployments/${deployment_owner}/${deployment_name}/predictions`,\n    {\n      method: \"POST\",\n      data: { ...data, stream },\n    }\n  );\n\n  return response.json();\n}\n\nmodule.exports = {\n  predictions: {\n    create: createPrediction,\n  },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi9kZXBsb3ltZW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsUUFBUTtBQUNuQixXQUFXLFVBQVU7QUFDckIsYUFBYSxpQkFBaUI7QUFDOUI7QUFDQTtBQUNBLFVBQVUsa0JBQWtCOztBQUU1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0IsaUJBQWlCLEdBQUcsZ0JBQWdCO0FBQ3hEO0FBQ0E7QUFDQSxjQUFjLGlCQUFpQjtBQUMvQjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmljb2xlLXN0b3JpZXMtYXBwLy4vbm9kZV9tb2R1bGVzL3JlcGxpY2F0ZS9saWIvZGVwbG95bWVudHMuanM/ZGNmOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENyZWF0ZSBhIG5ldyBwcmVkaWN0aW9uIHdpdGggYSBkZXBsb3ltZW50XG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGRlcGxveW1lbnRfb3duZXIgLSBSZXF1aXJlZC4gVGhlIHVzZXJuYW1lIG9mIHRoZSB1c2VyIG9yIG9yZ2FuaXphdGlvbiB3aG8gb3ducyB0aGUgZGVwbG95bWVudFxuICogQHBhcmFtIHtzdHJpbmd9IGRlcGxveW1lbnRfbmFtZSAtIFJlcXVpcmVkLiBUaGUgbmFtZSBvZiB0aGUgZGVwbG95bWVudFxuICogQHBhcmFtIHtvYmplY3R9IG9wdGlvbnNcbiAqIEBwYXJhbSB7b2JqZWN0fSBvcHRpb25zLmlucHV0IC0gUmVxdWlyZWQuIEFuIG9iamVjdCB3aXRoIHRoZSBtb2RlbCBpbnB1dHNcbiAqIEBwYXJhbSB7Ym9vbGVhbn0gW29wdGlvbnMuc3RyZWFtXSAtIFdoZXRoZXIgdG8gc3RyZWFtIHRoZSBwcmVkaWN0aW9uIG91dHB1dC4gRGVmYXVsdHMgdG8gZmFsc2VcbiAqIEBwYXJhbSB7c3RyaW5nfSBbb3B0aW9ucy53ZWJob29rXSAtIEFuIEhUVFBTIFVSTCBmb3IgcmVjZWl2aW5nIGEgd2ViaG9vayB3aGVuIHRoZSBwcmVkaWN0aW9uIGhhcyBuZXcgb3V0cHV0XG4gKiBAcGFyYW0ge3N0cmluZ1tdfSBbb3B0aW9ucy53ZWJob29rX2V2ZW50c19maWx0ZXJdIC0gWW91IGNhbiBjaGFuZ2Ugd2hpY2ggZXZlbnRzIHRyaWdnZXIgd2ViaG9vayByZXF1ZXN0cyBieSBzcGVjaWZ5aW5nIHdlYmhvb2sgZXZlbnRzIChgc3RhcnRgfGBvdXRwdXRgfGBsb2dzYHxgY29tcGxldGVkYClcbiAqIEByZXR1cm5zIHtQcm9taXNlPG9iamVjdD59IFJlc29sdmVzIHdpdGggdGhlIGNyZWF0ZWQgcHJlZGljdGlvbiBkYXRhXG4gKi9cbmFzeW5jIGZ1bmN0aW9uIGNyZWF0ZVByZWRpY3Rpb24oZGVwbG95bWVudF9vd25lciwgZGVwbG95bWVudF9uYW1lLCBvcHRpb25zKSB7XG4gIGNvbnN0IHsgc3RyZWFtLCAuLi5kYXRhIH0gPSBvcHRpb25zO1xuXG4gIGlmIChkYXRhLndlYmhvb2spIHtcbiAgICB0cnkge1xuICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLW5ld1xuICAgICAgbmV3IFVSTChkYXRhLndlYmhvb2spO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCB3ZWJob29rIFVSTFwiKTtcbiAgICB9XG4gIH1cblxuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdChcbiAgICBgL2RlcGxveW1lbnRzLyR7ZGVwbG95bWVudF9vd25lcn0vJHtkZXBsb3ltZW50X25hbWV9L3ByZWRpY3Rpb25zYCxcbiAgICB7XG4gICAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgICAgZGF0YTogeyAuLi5kYXRhLCBzdHJlYW0gfSxcbiAgICB9XG4gICk7XG5cbiAgcmV0dXJuIHJlc3BvbnNlLmpzb24oKTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIHByZWRpY3Rpb25zOiB7XG4gICAgY3JlYXRlOiBjcmVhdGVQcmVkaWN0aW9uLFxuICB9LFxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/deployments.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/error.js":
/*!*********************************************!*\
  !*** ./node_modules/replicate/lib/error.js ***!
  \*********************************************/
/***/ ((module) => {

eval("/**\n * A representation of an API error.\n */\nclass ApiError extends Error {\n  /**\n   * Creates a representation of an API error.\n   *\n   * @param {string} message - Error message\n   * @param {Request} request - HTTP request\n   * @param {Response} response - HTTP response\n   * @returns {ApiError} - An instance of ApiError\n   */\n  constructor(message, request, response) {\n    super(message);\n    this.name = \"ApiError\";\n    this.request = request;\n    this.response = response;\n  }\n}\n\nmodule.exports = ApiError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi9lcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixhQUFhLFNBQVM7QUFDdEIsYUFBYSxVQUFVO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmljb2xlLXN0b3JpZXMtYXBwLy4vbm9kZV9tb2R1bGVzL3JlcGxpY2F0ZS9saWIvZXJyb3IuanM/YjYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEEgcmVwcmVzZW50YXRpb24gb2YgYW4gQVBJIGVycm9yLlxuICovXG5jbGFzcyBBcGlFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgLyoqXG4gICAqIENyZWF0ZXMgYSByZXByZXNlbnRhdGlvbiBvZiBhbiBBUEkgZXJyb3IuXG4gICAqXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBtZXNzYWdlIC0gRXJyb3IgbWVzc2FnZVxuICAgKiBAcGFyYW0ge1JlcXVlc3R9IHJlcXVlc3QgLSBIVFRQIHJlcXVlc3RcbiAgICogQHBhcmFtIHtSZXNwb25zZX0gcmVzcG9uc2UgLSBIVFRQIHJlc3BvbnNlXG4gICAqIEByZXR1cm5zIHtBcGlFcnJvcn0gLSBBbiBpbnN0YW5jZSBvZiBBcGlFcnJvclxuICAgKi9cbiAgY29uc3RydWN0b3IobWVzc2FnZSwgcmVxdWVzdCwgcmVzcG9uc2UpIHtcbiAgICBzdXBlcihtZXNzYWdlKTtcbiAgICB0aGlzLm5hbWUgPSBcIkFwaUVycm9yXCI7XG4gICAgdGhpcy5yZXF1ZXN0ID0gcmVxdWVzdDtcbiAgICB0aGlzLnJlc3BvbnNlID0gcmVzcG9uc2U7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBBcGlFcnJvcjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/hardware.js":
/*!************************************************!*\
  !*** ./node_modules/replicate/lib/hardware.js ***!
  \************************************************/
/***/ ((module) => {

eval("/**\n * List hardware\n *\n * @returns {Promise<object[]>} Resolves with the array of hardware\n */\nasync function listHardware() {\n  const response = await this.request(\"/hardware\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  list: listHardware,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi9oYXJkd2FyZS5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLG1CQUFtQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uaWNvbGUtc3Rvcmllcy1hcHAvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi9oYXJkd2FyZS5qcz8xOTJiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTGlzdCBoYXJkd2FyZVxuICpcbiAqIEByZXR1cm5zIHtQcm9taXNlPG9iamVjdFtdPn0gUmVzb2x2ZXMgd2l0aCB0aGUgYXJyYXkgb2YgaGFyZHdhcmVcbiAqL1xuYXN5bmMgZnVuY3Rpb24gbGlzdEhhcmR3YXJlKCkge1xuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdChcIi9oYXJkd2FyZVwiLCB7XG4gICAgbWV0aG9kOiBcIkdFVFwiLFxuICB9KTtcblxuICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgbGlzdDogbGlzdEhhcmR3YXJlLFxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/hardware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/identifier.js":
/*!**************************************************!*\
  !*** ./node_modules/replicate/lib/identifier.js ***!
  \**************************************************/
/***/ ((module) => {

eval("/*\n * A reference to a model version in the format `owner/name` or `owner/name:version`.\n */\nclass ModelVersionIdentifier {\n  /*\n   * @param {string} Required. The model owner.\n   * @param {string} Required. The model name.\n   * @param {string} The model version.\n   */\n  constructor(owner, name, version = null) {\n    this.owner = owner;\n    this.name = name;\n    this.version = version;\n  }\n\n  /*\n   * Parse a reference to a model version\n   *\n   * @param {string}\n   * @returns {ModelVersionIdentifier}\n   * @throws {Error} If the reference is invalid.\n   */\n  static parse(ref) {\n    const match = ref.match(\n      /^(?<owner>[^/]+)\\/(?<name>[^/:]+)(:(?<version>.+))?$/\n    );\n    if (!match) {\n      throw new Error(\n        `Invalid reference to model version: ${ref}. Expected format: owner/name or owner/name:version`\n      );\n    }\n\n    const { owner, name, version } = match.groups;\n\n    return new ModelVersionIdentifier(owner, name, version);\n  }\n}\n\nmodule.exports = ModelVersionIdentifier;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/identifier.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/models.js":
/*!**********************************************!*\
  !*** ./node_modules/replicate/lib/models.js ***!
  \**********************************************/
/***/ ((module) => {

eval("/**\n * Get information about a model\n *\n * @param {string} model_owner - Required. The name of the user or organization that owns the model\n * @param {string} model_name - Required. The name of the model\n * @returns {Promise<object>} Resolves with the model data\n */\nasync function getModel(model_owner, model_name) {\n  const response = await this.request(`/models/${model_owner}/${model_name}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * List model versions\n *\n * @param {string} model_owner - Required. The name of the user or organization that owns the model\n * @param {string} model_name - Required. The name of the model\n * @returns {Promise<object>} Resolves with the list of model versions\n */\nasync function listModelVersions(model_owner, model_name) {\n  const response = await this.request(\n    `/models/${model_owner}/${model_name}/versions`,\n    {\n      method: \"GET\",\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * Get a specific model version\n *\n * @param {string} model_owner - Required. The name of the user or organization that owns the model\n * @param {string} model_name - Required. The name of the model\n * @param {string} version_id - Required. The model version\n * @returns {Promise<object>} Resolves with the model version data\n */\nasync function getModelVersion(model_owner, model_name, version_id) {\n  const response = await this.request(\n    `/models/${model_owner}/${model_name}/versions/${version_id}`,\n    {\n      method: \"GET\",\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * List all public models\n *\n * @returns {Promise<object>} Resolves with the model version data\n */\nasync function listModels() {\n  const response = await this.request(\"/models\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Create a new model\n *\n * @param {string} model_owner - Required. The name of the user or organization that will own the model. This must be the same as the user or organization that is making the API request. In other words, the API token used in the request must belong to this user or organization.\n * @param {string} model_name - Required. The name of the model. This must be unique among all models owned by the user or organization.\n * @param {object} options\n * @param {(\"public\"|\"private\")} options.visibility - Required. Whether the model should be public or private. A public model can be viewed and run by anyone, whereas a private model can be viewed and run only by the user or organization members that own the model.\n * @param {string} options.hardware - Required. The SKU for the hardware used to run the model. Possible values can be found by calling `Replicate.hardware.list()`.\n * @param {string} options.description - A description of the model.\n * @param {string} options.github_url - A URL for the model's source code on GitHub.\n * @param {string} options.paper_url - A URL for the model's paper.\n * @param {string} options.license_url - A URL for the model's license.\n * @param {string} options.cover_image_url - A URL for the model's cover image. This should be an image file.\n * @returns {Promise<object>} Resolves with the model version data\n */\nasync function createModel(model_owner, model_name, options) {\n  const data = { owner: model_owner, name: model_name, ...options };\n\n  const response = await this.request(\"/models\", {\n    method: \"POST\",\n    data,\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  get: getModel,\n  list: listModels,\n  create: createModel,\n  versions: { list: listModelVersions, get: getModelVersion },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/models.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/predictions.js":
/*!***************************************************!*\
  !*** ./node_modules/replicate/lib/predictions.js ***!
  \***************************************************/
/***/ ((module) => {

eval("/**\n * Create a new prediction\n *\n * @param {object} options\n * @param {string} options.model - The model.\n * @param {string} options.version - The model version.\n * @param {object} options.input - Required. An object with the model inputs\n * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n * @param {boolean} [options.stream] - Whether to stream the prediction output. Defaults to false\n * @returns {Promise<object>} Resolves with the created prediction\n */\nasync function createPrediction(options) {\n  const { model, version, stream, ...data } = options;\n\n  if (data.webhook) {\n    try {\n      // eslint-disable-next-line no-new\n      new URL(data.webhook);\n    } catch (err) {\n      throw new Error(\"Invalid webhook URL\");\n    }\n  }\n\n  let response;\n  if (version) {\n    response = await this.request(\"/predictions\", {\n      method: \"POST\",\n      data: { ...data, stream, version },\n    });\n  } else if (model) {\n    response = await this.request(`/models/${model}/predictions`, {\n      method: \"POST\",\n      data: { ...data, stream },\n    });\n  } else {\n    throw new Error(\"Either model or version must be specified\");\n  }\n\n  return response.json();\n}\n\n/**\n * Fetch a prediction by ID\n *\n * @param {number} prediction_id - Required. The prediction ID\n * @returns {Promise<object>} Resolves with the prediction data\n */\nasync function getPrediction(prediction_id) {\n  const response = await this.request(`/predictions/${prediction_id}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Cancel a prediction by ID\n *\n * @param {string} prediction_id - Required. The training ID\n * @returns {Promise<object>} Resolves with the data for the training\n */\nasync function cancelPrediction(prediction_id) {\n  const response = await this.request(`/predictions/${prediction_id}/cancel`, {\n    method: \"POST\",\n  });\n\n  return response.json();\n}\n\n/**\n * List all predictions\n *\n * @returns {Promise<object>} - Resolves with a page of predictions\n */\nasync function listPredictions() {\n  const response = await this.request(\"/predictions\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  create: createPrediction,\n  get: getPrediction,\n  cancel: cancelPrediction,\n  list: listPredictions,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/predictions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/stream.js":
/*!**********************************************!*\
  !*** ./node_modules/replicate/lib/stream.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Attempt to use readable-stream if available, attempt to use the built-in stream module.\nlet Readable;\ntry {\n  Readable = (__webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/lib/ours/index.js\").Readable);\n} catch (e) {\n  try {\n    Readable = (__webpack_require__(/*! stream */ \"stream\").Readable);\n  } catch (e) {\n    Readable = null;\n  }\n}\n\n/**\n * A server-sent event.\n */\nclass ServerSentEvent {\n  /**\n   * Create a new server-sent event.\n   *\n   * @param {string} event The event name.\n   * @param {string} data The event data.\n   * @param {string} id The event ID.\n   * @param {number} retry The retry time.\n   */\n  constructor(event, data, id, retry) {\n    this.event = event;\n    this.data = data;\n    this.id = id;\n    this.retry = retry;\n  }\n\n  /**\n   * Convert the event to a string.\n   */\n  toString() {\n    if (this.event === \"output\") {\n      return this.data;\n    }\n\n    return \"\";\n  }\n}\n\n/**\n * A stream of server-sent events.\n */\nclass Stream extends Readable {\n  /**\n   * Create a new stream of server-sent events.\n   *\n   * @param {string} url The URL to connect to.\n   * @param {object} options The fetch options.\n   */\n  constructor(url, options) {\n    if (!Readable) {\n      throw new Error(\n        \"Readable streams are not supported. Please use Node.js 18 or later, or install the readable-stream package.\"\n      );\n    }\n\n    super();\n    this.url = url;\n    this.options = options;\n\n    this.event = null;\n    this.data = [];\n    this.lastEventId = null;\n    this.retry = null;\n  }\n\n  decode(line) {\n    if (!line) {\n      if (!this.event && !this.data.length && !this.lastEventId) {\n        return null;\n      }\n\n      const sse = new ServerSentEvent(\n        this.event,\n        this.data.join(\"\\n\"),\n        this.lastEventId\n      );\n\n      this.event = null;\n      this.data = [];\n      this.retry = null;\n\n      return sse;\n    }\n\n    if (line.startsWith(\":\")) {\n      return null;\n    }\n\n    const [field, value] = line.split(\": \");\n    if (field === \"event\") {\n      this.event = value;\n    } else if (field === \"data\") {\n      this.data.push(value);\n    } else if (field === \"id\") {\n      this.lastEventId = value;\n    }\n\n    return null;\n  }\n\n  async *[Symbol.asyncIterator]() {\n    const response = await fetch(this.url, {\n      ...this.options,\n      headers: {\n        Accept: \"text/event-stream\",\n      },\n    });\n\n    for await (const chunk of response.body) {\n      const decoder = new TextDecoder(\"utf-8\");\n      const text = decoder.decode(chunk);\n      const lines = text.split(\"\\n\");\n      for (const line of lines) {\n        const sse = this.decode(line);\n        if (sse) {\n          if (sse.event === \"error\") {\n            throw new Error(sse.data);\n          }\n\n          yield sse;\n\n          if (sse.event === \"done\") {\n            return;\n          }\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = {\n  Stream,\n  ServerSentEvent,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/stream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/trainings.js":
/*!*************************************************!*\
  !*** ./node_modules/replicate/lib/trainings.js ***!
  \*************************************************/
/***/ ((module) => {

eval("/**\n * Create a new training\n *\n * @param {string} model_owner - Required. The username of the user or organization who owns the model\n * @param {string} model_name - Required. The name of the model\n * @param {string} version_id - Required. The version ID\n * @param {object} options\n * @param {string} options.destination - Required. The destination for the trained version in the form \"{username}/{model_name}\"\n * @param {object} options.input - Required. An object with the model inputs\n * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the training updates\n * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n * @returns {Promise<object>} Resolves with the data for the created training\n */\nasync function createTraining(model_owner, model_name, version_id, options) {\n  const { ...data } = options;\n\n  if (data.webhook) {\n    try {\n      // eslint-disable-next-line no-new\n      new URL(data.webhook);\n    } catch (err) {\n      throw new Error(\"Invalid webhook URL\");\n    }\n  }\n\n  const response = await this.request(\n    `/models/${model_owner}/${model_name}/versions/${version_id}/trainings`,\n    {\n      method: \"POST\",\n      data,\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * Fetch a training by ID\n *\n * @param {string} training_id - Required. The training ID\n * @returns {Promise<object>} Resolves with the data for the training\n */\nasync function getTraining(training_id) {\n  const response = await this.request(`/trainings/${training_id}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Cancel a training by ID\n *\n * @param {string} training_id - Required. The training ID\n * @returns {Promise<object>} Resolves with the data for the training\n */\nasync function cancelTraining(training_id) {\n  const response = await this.request(`/trainings/${training_id}/cancel`, {\n    method: \"POST\",\n  });\n\n  return response.json();\n}\n\n/**\n * List all trainings\n *\n * @returns {Promise<object>} - Resolves with a page of trainings\n */\nasync function listTrainings() {\n  const response = await this.request(\"/trainings\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  create: createTraining,\n  get: getTraining,\n  cancel: cancelTraining,\n  list: listTrainings,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/trainings.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/util.js":
/*!********************************************!*\
  !*** ./node_modules/replicate/lib/util.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ApiError = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/replicate/lib/error.js\");\n\n/**\n * Automatically retry a request if it fails with an appropriate status code.\n *\n * A GET request is retried if it fails with a 429 or 5xx status code.\n * A non-GET request is retried only if it fails with a 429 status code.\n *\n * If the response sets a Retry-After header,\n * the request is retried after the number of seconds specified in the header.\n * Otherwise, the request is retried after the specified interval,\n * with exponential backoff and jitter.\n *\n * @param {Function} request - A function that returns a Promise that resolves with a Response object\n * @param {object} options\n * @param {Function} [options.shouldRetry] - A function that returns true if the request should be retried\n * @param {number} [options.maxRetries] - Maximum number of retries. Defaults to 5\n * @param {number} [options.interval] - Interval between retries in milliseconds. Defaults to 500\n * @returns {Promise<Response>} - Resolves with the response object\n * @throws {ApiError} If the request failed\n */\nasync function withAutomaticRetries(request, options = {}) {\n  const shouldRetry = options.shouldRetry || (() => false);\n  const maxRetries = options.maxRetries || 5;\n  const interval = options.interval || 500;\n  const jitter = options.jitter || 100;\n\n  // eslint-disable-next-line no-promise-executor-return\n  const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\n\n  let attempts = 0;\n  do {\n    let delay = interval * 2 ** attempts + Math.random() * jitter;\n\n    /* eslint-disable no-await-in-loop */\n    try {\n      const response = await request();\n      if (response.ok || !shouldRetry(response)) {\n        return response;\n      }\n    } catch (error) {\n      if (error instanceof ApiError) {\n        const retryAfter = error.response.headers.get(\"Retry-After\");\n        if (retryAfter) {\n          if (!Number.isInteger(retryAfter)) {\n            // Retry-After is a date\n            const date = new Date(retryAfter);\n            if (!Number.isNaN(date.getTime())) {\n              delay = date.getTime() - new Date().getTime();\n            }\n          } else {\n            // Retry-After is a number of seconds\n            delay = retryAfter * 1000;\n          }\n        }\n      }\n    }\n\n    if (Number.isInteger(maxRetries) && maxRetries > 0) {\n      if (Number.isInteger(delay) && delay > 0) {\n        await sleep(interval * 2 ** (options.maxRetries - maxRetries));\n      }\n      attempts += 1;\n    }\n    /* eslint-enable no-await-in-loop */\n  } while (attempts < maxRetries);\n\n  return request();\n}\n\nmodule.exports = { withAutomaticRetries };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/util.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/package.json":
/*!*********************************************!*\
  !*** ./node_modules/replicate/package.json ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"replicate","version":"0.25.2","description":"JavaScript client for Replicate","repository":"github:replicate/replicate-javascript","homepage":"https://github.com/replicate/replicate-javascript#readme","bugs":"https://github.com/replicate/replicate-javascript/issues","license":"Apache-2.0","main":"index.js","engines":{"node":">=18.0.0","npm":">=7.19.0","git":">=2.11.0","yarn":">=1.7.0"},"scripts":{"check":"tsc","format":"biome format . --write","lint":"biome lint .","test":"jest"},"optionalDependencies":{"readable-stream":">=4.0.0"},"devDependencies":{"@biomejs/biome":"^1.4.1","@types/jest":"^29.5.3","@typescript-eslint/eslint-plugin":"^5.56.0","cross-fetch":"^3.1.5","jest":"^29.6.2","nock":"^13.3.0","ts-jest":"^29.1.0","typescript":"^5.0.2"}}');

/***/ })

};
;