// n8n Code Node for Kokoro MCP Space
// استخدام aiqcamp/MCP-kokoro Space مجاناً مع صوت Nicole

const text = $input.item.json.text || $input.item.json.story_text;
const voice = 'af_nicole'; // صوت Nicole الأمريكي 🎧
const speed = 0.9; // سرعة مناسبة للقصص

// تنظيف النص للحصول على أفضل نتيجة
function cleanTextForTTS(inputText) {
  return inputText
    .replace(/\n+/g, ' ') // استبدال أسطر جديدة بمسافات
    .replace(/\s+/g, ' ') // تنظيف المسافات المتعددة
    .replace(/[""]/g, '"') // توحيد علامات التنصيص
    .trim();
}

const cleanedText = cleanTextForTTS(text);

try {
  console.log(`Generating TTS for ${cleanedText.length} characters with voice: ${voice}`);
  
  // إرسال الطلب إلى MCP-kokoro Space
  const response = await $http.request({
    method: 'POST',
    url: 'https://aiqcamp-mcp-kokoro.hf.space/api/predict',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: {
      data: [
        cleanedText,  // النص المنظف
        voice,        // af_nicole
        speed         // 0.9
      ]
    },
    timeout: 120000 // دقيقتين timeout للنصوص الطويلة
  });

  console.log('MCP Response received:', response.status);

  // التحقق من نجاح الاستجابة
  if (response.body && response.body.data) {
    const responseData = response.body.data;
    
    // البحث عن رابط الملف الصوتي
    let audioUrl = null;
    
    if (Array.isArray(responseData)) {
      // البحث في المصفوفة عن رابط الصوت
      for (const item of responseData) {
        if (typeof item === 'string' && item.includes('.wav')) {
          audioUrl = item;
          break;
        } else if (item && typeof item === 'object' && item.url) {
          audioUrl = item.url;
          break;
        }
      }
    } else if (responseData.url) {
      audioUrl = responseData.url;
    }

    if (audioUrl) {
      // التأكد من أن الرابط كامل
      if (audioUrl.startsWith('/')) {
        audioUrl = 'https://aiqcamp-mcp-kokoro.hf.space' + audioUrl;
      }
      
      console.log('Audio URL generated:', audioUrl);
      
      return {
        json: {
          success: true,
          audioUrl: audioUrl,
          voice: voice,
          speed: speed,
          textLength: cleanedText.length,
          originalTextLength: text.length,
          provider: 'mcp_kokoro',
          processingTime: new Date().toISOString()
        }
      };
    } else {
      throw new Error('No audio URL found in response');
    }
  } else {
    throw new Error('Invalid response format from MCP-kokoro');
  }

} catch (error) {
  console.error('MCP-Kokoro Error:', error.message);
  
  // في حالة فشل MCP، جرب الـ Space الأصلي كـ fallback
  try {
    console.log('Trying fallback to original kokoro-web space...');
    
    const fallbackResponse = await $http.request({
      method: 'POST',
      url: 'https://webml-community-kokoro-web.hf.space/api/predict',
      headers: {
        'Content-Type': 'application/json'
      },
      body: {
        data: [cleanedText, voice, speed, false] // false = use CPU
      },
      timeout: 120000
    });

    if (fallbackResponse.body && fallbackResponse.body.data && fallbackResponse.body.data[0]) {
      const audioData = fallbackResponse.body.data[0];
      let fallbackUrl = audioData.url || audioData;
      
      if (fallbackUrl.startsWith('/')) {
        fallbackUrl = 'https://webml-community-kokoro-web.hf.space' + fallbackUrl;
      }
      
      console.log('Fallback successful:', fallbackUrl);
      
      return {
        json: {
          success: true,
          audioUrl: fallbackUrl,
          voice: voice,
          speed: speed,
          textLength: cleanedText.length,
          provider: 'kokoro_web_fallback',
          note: 'Used fallback after MCP failed'
        }
      };
    }
  } catch (fallbackError) {
    console.error('Fallback also failed:', fallbackError.message);
  }
  
  return {
    json: {
      success: false,
      error: error.message,
      textLength: cleanedText.length,
      voice: voice,
      provider: 'mcp_kokoro_failed',
      timestamp: new Date().toISOString()
    }
  };
}
