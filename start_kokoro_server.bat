@echo off
echo Starting Kokoro TTS Web Application...

REM Check if virtual environment exists
if not exist "kokoro_env\Scripts\activate.bat" (
    echo Virtual environment not found!
    echo Please run install_kokoro.bat first
    pause
    exit /b 1
)

REM Activate virtual environment
call kokoro_env\Scripts\activate.bat

REM Check if kokoro is installed
python -c "import kokoro" >nul 2>&1
if errorlevel 1 (
    echo Kokoro not installed!
    echo Please run install_kokoro.bat first
    pause
    exit /b 1
)

REM Create templates directory if it doesn't exist
if not exist "templates" mkdir templates

echo.
echo ========================================
echo Kokoro TTS Web Application Starting...
echo ========================================
echo.
echo 🌐 Web Interface: http://localhost:5000
echo 📊 Statistics: http://localhost:5000/stats
echo 🔧 Health Check: http://localhost:5000/health
echo.
echo 📡 API Endpoints:
echo - POST /api/predict  - Gradio-compatible API
echo - POST /tts         - Direct TTS API
echo - POST /tts_url     - TTS with URL response
echo - GET  /voices      - Available voices
echo.
echo 🎧 Available Voices:
echo - af_nicole (🇺🇸 🚺 Nicole 🎧) - Recommended for stories
echo - af_heart  (🇺🇸 🚺 Heart ❤️)
echo - af_bella  (🇺🇸 🚺 Bella 🔥)
echo - am_michael (🇺🇸 🚹 Michael)
echo - bf_emma   (🇬🇧 🚺 Emma)
echo - bm_george (🇬🇧 🚹 George)
echo.
echo Press Ctrl+C to stop the server
echo Opening browser in 3 seconds...
echo.

REM Wait 3 seconds then open browser
timeout /t 3 /nobreak >nul
start http://localhost:5000

REM Start the server
python kokoro_server.py
