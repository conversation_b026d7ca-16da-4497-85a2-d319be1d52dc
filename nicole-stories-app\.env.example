# 🎧 Nicole Stories App - Environment Configuration

# ================================
# 🤖 AI Story Generation (Optional)
# ================================
# Get your key from: https://platform.openai.com/api-keys
# Cost: ~$0.002 per story
OPENAI_API_KEY=

# ================================
# 🎤 Text-to-Speech (Choose One)
# ================================

# Option 1: Replicate (Paid, Most Reliable)
# Get token from: https://replicate.com/account/api-tokens
# Cost: ~$0.002 per second of audio
REPLICATE_API_TOKEN=

# Option 2: Google Colab (Free, Setup Required)
# Follow setup guide in README.md
GOOGLE_COLAB_ENDPOINT=https://your-colab-endpoint.ngrok.io/generate

# ================================
# 🌐 App Configuration
# ================================
NEXT_PUBLIC_APP_URL=http://localhost:3000

# ================================
# 📝 Notes
# ================================
# - App works without any keys (fallback mode)
# - OpenAI key enables AI story generation
# - TTS keys enable voice conversion
# - See README.md for detailed setup
