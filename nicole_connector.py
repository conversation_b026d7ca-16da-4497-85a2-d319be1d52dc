#!/usr/bin/env python3
"""
Nicole TTS Connector - ربط HuggingFace Spaces مع حاسوبك
"""

import requests
import json
import os
import time
from datetime import datetime

class NicoleTTS:
    def __init__(self):
        self.endpoints = [
            'https://aiqcamp-mcp-kokoro.hf.space/api/predict',
            'https://webml-community-kokoro-web.hf.space/api/predict'
        ]
        self.voice = 'af_nicole'  # Nicole الأمريكية
        self.speed = 0.85  # سرعة مناسبة للقصص
        
    def generate_audio(self, text, output_file=None):
        """
        إنتاج الصوت من النص
        """
        print(f"🎧 Nicole تقرأ: {text[:50]}...")
        
        for i, endpoint in enumerate(self.endpoints, 1):
            try:
                print(f"🔄 جاري المحاولة {i}/{len(self.endpoints)}: {endpoint.split('//')[1].split('.')[0]}")
                
                # تحضير البيانات
                if 'mcp-kokoro' in endpoint:
                    payload = {'data': [text, self.voice, self.speed]}
                else:
                    payload = {'data': [text, self.voice, self.speed, False]}
                
                # إرسال الطلب
                response = requests.post(
                    endpoint,
                    json=payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=120
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if result.get('data') and result['data'][0]:
                        audio_data = result['data'][0]
                        audio_url = audio_data.get('url') if isinstance(audio_data, dict) else audio_data
                        
                        if audio_url and audio_url.startswith('/'):
                            base_url = endpoint.replace('/api/predict', '')
                            audio_url = base_url + audio_url
                        
                        if audio_url:
                            print(f"✅ نجح! رابط الصوت: {audio_url}")
                            
                            # تحميل الصوت إذا طُلب ذلك
                            if output_file:
                                self.download_audio(audio_url, output_file)
                            
                            return {
                                'success': True,
                                'audio_url': audio_url,
                                'text_length': len(text),
                                'voice': 'Nicole 🇺🇸 🚺',
                                'provider': endpoint.split('//')[1].split('.')[0]
                            }
                
            except Exception as e:
                print(f"❌ فشل {endpoint.split('//')[1].split('.')[0]}: {e}")
                continue
        
        return {'success': False, 'error': 'جميع الخدمات غير متاحة حالياً'}
    
    def download_audio(self, audio_url, output_file):
        """
        تحميل الصوت إلى ملف محلي
        """
        try:
            print(f"📥 جاري تحميل الصوت إلى: {output_file}")
            
            audio_response = requests.get(audio_url, timeout=60)
            if audio_response.status_code == 200:
                with open(output_file, 'wb') as f:
                    f.write(audio_response.content)
                print(f"✅ تم التحميل بنجاح: {output_file}")
                return True
            else:
                print(f"❌ فشل التحميل: {audio_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في التحميل: {e}")
            return False
    
    def generate_story_audio(self, story_text, story_name="bedtime_story"):
        """
        إنتاج صوت للقصة مع حفظ تلقائي
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"{story_name}_{timestamp}.wav"
        
        result = self.generate_audio(story_text, output_file)
        
        if result['success']:
            result['local_file'] = output_file
        
        return result

# مثال للاستخدام
def main():
    print("🎧 Nicole TTS Connector")
    print("=" * 50)
    
    # إنشاء كائن Nicole
    nicole = NicoleTTS()
    
    # قصة تجريبية
    story = """Once upon a time, in a magical forest far away, there lived a little rabbit named Luna. 
    Every night before bed, Luna would look up at the twinkling stars and make a wish for sweet dreams 
    and wonderful adventures."""
    
    print("📖 القصة التجريبية:")
    print(story)
    print("\n" + "=" * 50)
    
    # إنتاج الصوت
    result = nicole.generate_story_audio(story, "luna_rabbit_story")
    
    if result['success']:
        print(f"\n🎵 تم إنتاج الصوت بنجاح!")
        print(f"📁 الملف المحلي: {result.get('local_file', 'غير محفوظ')}")
        print(f"🌐 الرابط الأصلي: {result['audio_url']}")
        print(f"🎤 الصوت: {result['voice']}")
        print(f"📊 طول النص: {result['text_length']} حرف")
        print(f"🏢 المزود: {result['provider']}")
    else:
        print(f"\n❌ فشل في إنتاج الصوت: {result.get('error', 'خطأ غير معروف')}")

if __name__ == "__main__":
    main()
