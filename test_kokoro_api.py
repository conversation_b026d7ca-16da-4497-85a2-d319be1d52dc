#!/usr/bin/env python3
"""
Test script for Kokoro TTS API
اختبار API كوكورو للتأكد من عمله مع n8n
"""

import requests
import json
import time

# API Base URL
BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing Health Check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_voices():
    """Test voices endpoint"""
    print("\n🎤 Testing Voices Endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/voices")
        print(f"Status: {response.status_code}")
        data = response.json()
        print(f"Available voices: {list(data['voices'].keys())}")
        print(f"Default voice: {data['default']}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Voices test failed: {e}")
        return False

def test_tts_url():
    """Test TTS URL endpoint (for n8n)"""
    print("\n🌐 Testing TTS URL Endpoint...")
    try:
        payload = {
            "text": "Hello from Kokoro TTS! This is a test for n8n integration.",
            "voice": "af_nicole",
            "speed": 1.0
        }
        
        response = requests.post(
            f"{BASE_URL}/tts_url",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status: {response.status_code}")
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")
        
        if data.get('success'):
            print(f"✅ Audio URL: {data.get('audio_url')}")
            return True
        else:
            print(f"❌ TTS failed: {data.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ TTS URL test failed: {e}")
        return False

def test_tts_file():
    """Test TTS file endpoint"""
    print("\n📁 Testing TTS File Endpoint...")
    try:
        payload = {
            "text": "This is a test audio file from Kokoro TTS.",
            "voice": "af_nicole",
            "speed": 1.0
        }
        
        response = requests.post(
            f"{BASE_URL}/tts",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the audio file
            with open("test_audio.wav", "wb") as f:
                f.write(response.content)
            print(f"✅ Audio file saved as: test_audio.wav")
            print(f"File size: {len(response.content)} bytes")
            return True
        else:
            print(f"❌ TTS file failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ TTS file test failed: {e}")
        return False

def test_stats():
    """Test stats endpoint"""
    print("\n📊 Testing Stats Endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/stats")
        print(f"Status: {response.status_code}")
        data = response.json()
        print(f"Stats: {json.dumps(data, indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Stats test failed: {e}")
        return False

def test_n8n_workflow():
    """Test n8n-style workflow"""
    print("\n🔄 Testing n8n Workflow Simulation...")
    try:
        # Simulate n8n webhook data
        webhook_data = {
            "text": "مرحبا! هذا اختبار لـ n8n مع Kokoro TTS. Perfect for bedtime stories!",
            "voice": "af_nicole",
            "speed": 0.9
        }
        
        print(f"Webhook Input: {json.dumps(webhook_data, indent=2)}")
        
        # Call TTS API (as n8n would do)
        response = requests.post(
            f"{BASE_URL}/tts_url",
            json=webhook_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"API Response Status: {response.status_code}")
        result = response.json()
        print(f"API Response: {json.dumps(result, indent=2)}")
        
        if result.get('success'):
            print("✅ n8n workflow simulation successful!")
            print(f"🎧 Audio URL for n8n: {result.get('audio_url')}")
            return True
        else:
            print(f"❌ n8n workflow failed: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ n8n workflow test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Kokoro TTS API Tests for n8n Integration")
    print("=" * 60)
    
    tests = [
        ("Health Check", test_health),
        ("Voices", test_voices),
        ("TTS URL (n8n)", test_tts_url),
        ("TTS File", test_tts_file),
        ("Stats", test_stats),
        ("n8n Workflow", test_n8n_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        success = test_func()
        results.append((test_name, success))
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! API is ready for n8n integration!")
        print("\n📋 Next Steps:")
        print("1. Import n8n_kokoro_api_workflow.json into n8n")
        print("2. Use http://localhost:8000/tts_url in your workflows")
        print("3. Check n8n_kokoro_guide.md for detailed instructions")
    else:
        print(f"\n⚠️ {len(results) - passed} tests failed. Check the errors above.")
        print("Make sure the API server is running: python kokoro_api_n8n.py")

if __name__ == "__main__":
    main()
