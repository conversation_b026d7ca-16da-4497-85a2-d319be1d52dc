// n8n Code Node - Kokoro TTS Simple Version
// استخدام بسيط لـ Kokoro مع صوت Nicole

const text = $input.item.json.text || $input.item.json.story_text;

// إعدادات Nicole للقصص
const settings = {
  voice: 'af_nicole',  // صوت Nicole الأمريكي 🎧
  speed: 0.9,          // سرعة هادئة للقصص
  lang_code: 'a'       // الإنجليزية الأمريكية
};

try {
  // تنظيف النص
  const cleanText = text
    .replace(/\n+/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  console.log(`Generating TTS: ${cleanText.length} chars with ${settings.voice}`);

  // إرسال الطلب
  const response = await $http.request({
    method: 'POST',
    url: 'https://aiqcamp-mcp-kokoro.hf.space/api/predict',
    headers: {
      'Content-Type': 'application/json'
    },
    body: {
      data: [cleanText, settings.voice, settings.speed]
    },
    timeout: 90000 // دقيقة ونصف
  });

  // استخراج رابط الصوت
  const audioUrl = response.body.data[0].url || response.body.data[0];
  
  // التأكد من الرابط الكامل
  const fullUrl = audioUrl.startsWith('/') 
    ? 'https://aiqcamp-mcp-kokoro.hf.space' + audioUrl 
    : audioUrl;

  return {
    json: {
      success: true,
      audioUrl: fullUrl,
      voice: settings.voice,
      textLength: cleanText.length,
      provider: 'mcp_kokoro'
    }
  };

} catch (error) {
  console.error('Kokoro Error:', error);
  
  return {
    json: {
      success: false,
      error: error.message,
      voice: settings.voice,
      textLength: text.length
    }
  };
}
