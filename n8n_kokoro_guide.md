# 🎧 Kokoro TTS API Guide for n8n

## 📋 Overview
This guide shows you how to use the **free and open-source Kokoro TTS API** with n8n workflows.

## 🚀 Quick Start

### 1. Start the API Server
```bash
python kokoro_api_n8n.py
```
The API will be available at: `http://localhost:8000`

### 2. API Endpoints

#### 🔍 Health Check
```
GET http://localhost:8000/health
```

#### 🎤 Get Available Voices
```
GET http://localhost:8000/voices
```

#### 🎵 Generate Audio File
```
POST http://localhost:8000/tts
Content-Type: application/json

{
  "text": "Hello from Kokoro TTS!",
  "voice": "af_nicole",
  "speed": 1.0
}
```
Returns: Audio file download

#### 🌐 Generate Audio URL
```
POST http://localhost:8000/tts_url
Content-Type: application/json

{
  "text": "Hello from Kokoro TTS!",
  "voice": "af_nicole", 
  "speed": 1.0
}
```
Returns: JSO<PERSON> with audio_url

## 🎤 Available Voices

### 🇺🇸 American English (Recommended)
- `af_nicole` - <PERSON> (US Female) 🎧 **Best for bedtime stories**
- `af_heart` - Heart (US Female) ❤️
- `af_bella` - Bella (US Female) 🔥
- `am_michael` - Michael (US Male)

### 🇬🇧 British English
- `bf_emma` - Emma (UK Female)
- `bm_george` - George (UK Male)

### 🇺🇸 Other American Voices
- `af_sarah` - Sarah (US Female)
- `af_sky` - Sky (US Female)

## 📱 n8n Integration Examples

### Example 1: Simple TTS Webhook
```json
{
  "nodes": [
    {
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook",
      "parameters": {
        "httpMethod": "POST",
        "path": "tts"
      }
    },
    {
      "name": "Kokoro TTS",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "http://localhost:8000/tts_url",
        "method": "POST",
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "text",
              "value": "={{ $json.text }}"
            },
            {
              "name": "voice", 
              "value": "af_nicole"
            }
          ]
        }
      }
    }
  ]
}
```

### Example 2: Bedtime Story Generator
```json
{
  "text": "Once upon a time, in a magical forest far away, there lived a little rabbit named Luna. Every night before bed, Luna would look up at the twinkling stars and make a wish for sweet dreams.",
  "voice": "af_nicole",
  "speed": 0.9
}
```

### Example 3: Multi-language Support
```json
{
  "text": "Good morning! How are you today?",
  "voice": "bf_emma",
  "speed": 1.1
}
```

## 🔧 n8n HTTP Request Node Setup

### Method: POST
### URL: `http://localhost:8000/tts_url`
### Headers:
```
Content-Type: application/json
```

### Body (JSON):
```json
{
  "text": "{{ $json.text }}",
  "voice": "{{ $json.voice || 'af_nicole' }}",
  "speed": "{{ $json.speed || 1.0 }}"
}
```

## 📊 Response Format

### Success Response:
```json
{
  "success": true,
  "audio_url": "http://localhost:8000/static/kokoro_12345_af_nicole.wav",
  "voice": "af_nicole",
  "text_length": 25,
  "duration": 3.2,
  "voice_info": {
    "name": "Nicole (US Female)",
    "lang": "en-US",
    "gender": "female",
    "recommended": true
  }
}
```

### Error Response:
```json
{
  "success": false,
  "error": "No text provided"
}
```

## 🎯 Use Cases for n8n

### 1. **Bedtime Stories (قصص قبل النوم)**
- Voice: `af_nicole` (Nicole)
- Speed: `0.9` (slower for relaxation)
- Perfect for children's content

### 2. **Notifications & Alerts**
- Voice: `am_michael` (Michael)
- Speed: `1.0` (normal)
- Clear and professional

### 3. **Educational Content**
- Voice: `bf_emma` (Emma)
- Speed: `1.1` (slightly faster)
- British accent for variety

### 4. **Automated Announcements**
- Voice: `af_heart` (Heart)
- Speed: `1.0`
- Warm and friendly tone

## 🔄 Workflow Examples

### Webhook → TTS → Email
1. Receive text via webhook
2. Convert to speech with Kokoro
3. Send audio file via email

### Schedule → TTS → Notification
1. Scheduled trigger (daily reminder)
2. Generate speech from template
3. Send to notification service

### Database → TTS → File Storage
1. Query database for content
2. Convert each record to speech
3. Save audio files to storage

## 🛠️ Installation Requirements

### For Full Functionality:
```bash
pip install kokoro soundfile torch flask flask-cors
```

### For Demo Mode:
```bash
pip install flask flask-cors
```

## 📈 Monitoring & Stats

### Get API Statistics:
```
GET http://localhost:8000/stats
```

Returns usage statistics, uptime, and voice usage data.

## 🔒 Security Notes

- API runs on localhost by default
- No authentication required for local use
- For production, add authentication
- Consider rate limiting for public APIs

## 🆘 Troubleshooting

### Common Issues:

1. **"Kokoro not available"**
   - Install: `pip install kokoro`
   - Check Python version compatibility

2. **"Connection refused"**
   - Ensure API server is running
   - Check port 8000 is available

3. **"No audio generated"**
   - Check text length (max 5000 chars)
   - Verify voice name is correct

4. **n8n can't reach API**
   - Use `http://localhost:8000` not `127.0.0.1`
   - Check firewall settings

## 💡 Tips for Best Results

1. **For Bedtime Stories:**
   - Use `af_nicole` voice
   - Set speed to `0.8-0.9`
   - Keep sentences short and simple

2. **For Professional Content:**
   - Use `am_michael` or `bf_emma`
   - Set speed to `1.0-1.1`
   - Use clear punctuation

3. **For Long Content:**
   - Split into smaller chunks
   - Use consistent voice and speed
   - Consider pauses between sections

## 🎉 Ready to Use!

Your Kokoro TTS API is now ready for n8n integration! 

Start with simple text-to-speech and expand to complex workflows as needed.
