@echo off
echo Starting Kokoro TTS in Production Mode...

REM Check if virtual environment exists
if not exist "kokoro_env\Scripts\activate.bat" (
    echo Virtual environment not found!
    echo Please run install_kokoro.bat first
    pause
    exit /b 1
)

REM Activate virtual environment
call kokoro_env\Scripts\activate.bat

REM Create necessary directories
if not exist "templates" mkdir templates
if not exist "static" mkdir static
if not exist "logs" mkdir logs

echo.
echo ========================================
echo Kokoro TTS Production Server
echo ========================================
echo.
echo 🌐 Web Interface: http://localhost:5000
echo 📊 API Endpoint: http://localhost:5000/api/predict
echo 📝 Logs: logs/kokoro.log
echo.
echo Starting with <PERSON>icorn for better performance...
echo Press Ctrl+C to stop the server
echo.

REM Start with Gunicorn for production
gunicorn --bind 0.0.0.0:5000 --workers 2 --timeout 120 --access-logfile logs/access.log --error-logfile logs/error.log kokoro_server:app
