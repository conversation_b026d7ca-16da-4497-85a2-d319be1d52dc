// n8n Code Node for Kokoro - Direct Implementation
// تشغيل Kokoro مباشرة في n8n (يتطلب Python environment)

const text = $input.item.json.text || $input.item.json.story_text;
const voice = 'af_nicole';
const speed = 0.9;

// Python script لتشغيل Kokoro
const pythonScript = `
import sys
import json
import tempfile
import os
from pathlib import Path

try:
    from kokoro import KPipeline
    import soundfile as sf
    import torch
    
    # Initialize pipeline
    pipeline = KPipeline(lang_code='a')
    
    # Input parameters
    text = """${text.replace(/"/g, '\\"')}"""
    voice = "${voice}"
    speed = ${speed}
    
    # Generate audio
    generator = pipeline(text, voice=voice, speed=speed)
    
    # Collect audio segments
    audio_segments = []
    for i, (gs, ps, audio) in enumerate(generator):
        audio_segments.append(audio)
    
    # Concatenate if multiple segments
    if len(audio_segments) == 1:
        final_audio = audio_segments[0]
    else:
        final_audio = torch.cat(audio_segments, dim=0)
    
    # Save to temporary file
    temp_dir = Path(tempfile.gettempdir())
    filename = f"kokoro_{hash(text)}_{voice}.wav"
    file_path = temp_dir / filename
    
    sf.write(str(file_path), final_audio.numpy(), 24000)
    
    # Return result
    result = {
        "success": True,
        "audioPath": str(file_path),
        "voice": voice,
        "duration": len(final_audio) / 24000,
        "textLength": len(text)
    }
    
    print(json.dumps(result))
    
except Exception as e:
    error_result = {
        "success": False,
        "error": str(e),
        "textLength": len(text) if 'text' in locals() else 0
    }
    print(json.dumps(error_result))
`;

try {
  // تشغيل Python script
  const { exec } = require('child_process');
  
  const result = await new Promise((resolve, reject) => {
    exec(`python -c "${pythonScript}"`, (error, stdout, stderr) => {
      if (error) {
        reject(new Error(`Python execution failed: ${error.message}`));
        return;
      }
      
      if (stderr) {
        console.warn('Python stderr:', stderr);
      }
      
      try {
        const result = JSON.parse(stdout.trim());
        resolve(result);
      } catch (parseError) {
        reject(new Error(`Failed to parse Python output: ${parseError.message}`));
      }
    });
  });
  
  if (result.success) {
    // قراءة الملف الصوتي وتحويله إلى base64
    const fs = require('fs');
    const audioBuffer = fs.readFileSync(result.audioPath);
    const audioBase64 = audioBuffer.toString('base64');
    
    return {
      json: {
        success: true,
        audioBase64: audioBase64,
        audioPath: result.audioPath,
        voice: result.voice,
        duration: result.duration,
        textLength: result.textLength,
        provider: 'kokoro_direct'
      }
    };
  } else {
    throw new Error(result.error);
  }

} catch (error) {
  console.error('Kokoro Direct Error:', error);
  
  return {
    json: {
      success: false,
      error: error.message,
      textLength: text.length,
      provider: 'kokoro_direct'
    }
  };
}
