@echo off
echo.
echo ========================================
echo 🎧 Nicole TTS - HuggingFace Connector
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python موجود

REM Install required packages
echo 📦 تثبيت المكتبات المطلوبة...
pip install requests tkinter >nul 2>&1

echo.
echo 🎯 اختر طريقة الاستخدام:
echo.
echo 1. 🖥️  واجهة سطح المكتب (سهلة)
echo 2. 💻 سطر الأوامر (للمطورين)
echo 3. 🌐 فتح HuggingFace مباشرة
echo 4. ❌ إلغاء
echo.

set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🖥️ تشغيل واجهة سطح المكتب...
    python nicole_desktop_app.py
) else if "%choice%"=="2" (
    echo.
    echo 💻 تشغيل سطر الأوامر...
    python nicole_connector.py
) else if "%choice%"=="3" (
    echo.
    echo 🌐 فتح HuggingFace Spaces...
    start https://aiqcamp-mcp-kokoro.hf.space
    start https://webml-community-kokoro-web.hf.space
) else if "%choice%"=="4" (
    echo.
    echo ❌ تم الإلغاء
) else (
    echo.
    echo ❌ اختيار غير صحيح
)

echo.
pause
