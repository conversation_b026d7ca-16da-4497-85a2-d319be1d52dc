<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nicole TTS - Direct HuggingFace</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        .container { max-width: 800px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; color: white; }
        .header h1 { font-size: 3rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #555; }
        .text-input {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            font-family: 'Georgia', serif;
        }
        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        .generate-btn:hover { transform: translateY(-2px); }
        .generate-btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .loading { 
            display: none; 
            text-align: center; 
            margin: 20px 0; 
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .loading.show { display: block; }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .result { 
            display: none; 
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
            border-radius: 10px; 
            padding: 20px; 
            margin-top: 20px; 
        }
        .result.show { display: block; }
        .audio-player { width: 100%; margin: 15px 0; border-radius: 10px; }
        .error { 
            display: none;
            background: #f8d7da; 
            color: #721c24; 
            padding: 15px; 
            border-radius: 10px; 
            margin: 10px 0; 
            border: 1px solid #f5c6cb;
        }
        .error.show { display: block; }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #bee5eb;
        }
        .download-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 8px;
            display: inline-block;
            margin-top: 10px;
            font-weight: 600;
        }
        .sample-stories {
            margin-top: 20px;
        }
        .story-sample {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid #e9ecef;
        }
        .story-sample:hover {
            background: #e3f2fd;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎧 Nicole TTS</h1>
            <p>Perfect American Female Voice for Bedtime Stories</p>
            <p style="font-size: 1.1rem; margin-top: 10px;">🇺🇸 🚺 Direct HuggingFace Integration</p>
        </div>

        <div class="card">
            <div class="info">
                <strong>🎤 Nicole Voice Features:</strong><br>
                • 🇺🇸 American English accent<br>
                • 🚺 Warm, gentle female voice<br>
                • 😴 Perfect for bedtime stories<br>
                • 🎧 High-quality audio output<br>
                • ⚡ Direct HuggingFace Spaces integration
            </div>

            <form id="ttsForm">
                <div class="form-group">
                    <label for="storyText">📖 Your Bedtime Story:</label>
                    <textarea 
                        id="storyText" 
                        name="storyText" 
                        class="text-input" 
                        placeholder="Once upon a time, in a magical land far away..."
                        required
                    >Once upon a time, in a magical forest far away, there lived a little rabbit named Luna. Every night before bed, Luna would look up at the twinkling stars and make a wish for sweet dreams and wonderful adventures.</textarea>
                </div>

                <button type="submit" class="generate-btn" id="generateBtn">
                    🎵 Generate Nicole's Voice
                </button>
            </form>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p><strong>🎧 Nicole is reading your story...</strong></p>
                <p>Creating beautiful audio for bedtime 🌙</p>
                <p><em>This may take 30-60 seconds...</em></p>
            </div>

            <div class="error" id="error">
                <strong>❌ Error:</strong> <span id="errorMessage"></span>
            </div>

            <div class="result" id="result">
                <h3>🎵 Your Story is Ready!</h3>
                <p><strong>Narrator:</strong> Nicole 🇺🇸 🚺</p>
                <p><strong>Story Length:</strong> <span id="resultLength"></span> characters</p>
                <p><strong>Provider:</strong> <span id="resultProvider"></span></p>
                
                <audio controls class="audio-player" id="audioPlayer">
                    Your browser does not support the audio element.
                </audio>
                
                <div id="downloadSection"></div>
            </div>

            <div class="sample-stories">
                <h3>📚 Sample Bedtime Stories (Click to Load):</h3>
                <div class="story-sample" onclick="loadSample(1)">
                    <strong>🐰 Luna the Magic Rabbit:</strong> A gentle tale about a rabbit who grants sweet dreams to children around the world.
                </div>
                <div class="story-sample" onclick="loadSample(2)">
                    <strong>🌟 The Sleepy Star:</strong> A soothing story about a star who helps children fall asleep peacefully.
                </div>
                <div class="story-sample" onclick="loadSample(3)">
                    <strong>🦋 Bella's Dream Garden:</strong> A colorful journey through a magical garden where dreams grow like flowers.
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample stories
        const sampleStories = {
            1: "Once upon a time, in a magical forest far away, there lived a little rabbit named Luna. Every night before bed, Luna would look up at the twinkling stars and make a wish for sweet dreams and wonderful adventures. One evening, as Luna was getting ready for sleep, she noticed a gentle glow coming from behind the old oak tree. Curious, she hopped over to investigate and discovered a tiny fairy sitting on a mushroom, sprinkling golden dust that sparkled like starlight. 'Hello, Luna,' said the fairy with a voice like tinkling bells. 'I'm here to grant you the sweetest dreams tonight.' And with that, Luna felt her eyelids grow heavy as the most wonderful dreams began to fill her mind.",
            
            2: "High up in the night sky, there was a little star named Stella who had a very special job. While all the other stars simply twinkled and shone, Stella's job was to watch over sleeping children and make sure they had the most beautiful dreams. Every night, Stella would float down from the sky on a cloud of silver moonbeams and visit children all around the world. She would whisper gentle lullabies and sprinkle dream dust that smelled like lavender and vanilla. Tonight, Stella is coming to visit you, bringing with her the most peaceful sleep and the sweetest dreams you've ever had.",
            
            3: "In a garden where flowers bloomed in every color of the rainbow, there lived a beautiful butterfly named Bella. Bella had wings that shimmered like jewels in the sunlight, and wherever she flew, she left a trail of sparkling fairy dust. Every evening, as the sun began to set, Bella would dance from flower to flower, collecting the sweet dreams that grew in each blossom. She would gather dreams of flying through cotton candy clouds, swimming with friendly dolphins, and playing in fields of soft, bouncing marshmallows. Tonight, Bella wants to share these magical dreams with you as you drift off to sleep."
        };

        // Load sample story
        function loadSample(num) {
            document.getElementById('storyText').value = sampleStories[num];
        }

        // Form submission
        document.getElementById('ttsForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const text = document.getElementById('storyText').value.trim();
            
            if (!text) {
                showError('Please enter some text for your story!');
                return;
            }

            if (text.length > 2000) {
                showError('Story is too long! Please keep it under 2000 characters for best results.');
                return;
            }

            // Show loading
            showLoading();

            try {
                // Try HuggingFace Spaces endpoints
                const endpoints = [
                    'https://aiqcamp-mcp-kokoro.hf.space/api/predict',
                    'https://webml-community-kokoro-web.hf.space/api/predict'
                ];

                let success = false;
                
                for (const endpoint of endpoints) {
                    try {
                        console.log(`Trying endpoint: ${endpoint}`);
                        
                        let payload;
                        if (endpoint.includes('mcp-kokoro')) {
                            payload = { data: [text, 'af_nicole', 0.85] };
                        } else {
                            payload = { data: [text, 'af_nicole', 0.85, false] };
                        }

                        const response = await fetch(endpoint, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        });

                        console.log(`Response status: ${response.status}`);

                        if (response.ok) {
                            const result = await response.json();
                            console.log('Response data:', result);

                            if (result.data && result.data[0]) {
                                let audioUrl = result.data[0];
                                
                                if (typeof audioUrl === 'object' && audioUrl.url) {
                                    audioUrl = audioUrl.url;
                                }

                                if (audioUrl && audioUrl.startsWith('/')) {
                                    const baseUrl = endpoint.replace('/api/predict', '');
                                    audioUrl = baseUrl + audioUrl;
                                }

                                if (audioUrl) {
                                    showResult(audioUrl, text.length, endpoint);
                                    success = true;
                                    break;
                                }
                            }
                        }
                    } catch (endpointError) {
                        console.warn(`Endpoint ${endpoint} failed:`, endpointError);
                        continue;
                    }
                }

                if (!success) {
                    throw new Error('All TTS services are currently unavailable. Please try again later or visit the HuggingFace Spaces directly.');
                }

            } catch (error) {
                console.error('TTS Error:', error);
                showError(error.message);
            } finally {
                hideLoading();
            }
        });

        function showLoading() {
            document.getElementById('loading').classList.add('show');
            document.getElementById('result').classList.remove('show');
            document.getElementById('error').classList.remove('show');
            document.getElementById('generateBtn').disabled = true;
        }

        function hideLoading() {
            document.getElementById('loading').classList.remove('show');
            document.getElementById('generateBtn').disabled = false;
        }

        function showResult(audioUrl, textLength, provider) {
            const audioPlayer = document.getElementById('audioPlayer');
            audioPlayer.src = audioUrl;
            
            document.getElementById('resultLength').textContent = textLength.toLocaleString();
            document.getElementById('resultProvider').textContent = provider.split('//')[1].split('.')[0];
            
            document.getElementById('downloadSection').innerHTML = `
                <a href="${audioUrl}" download="nicole_bedtime_story.wav" class="download-btn">
                    📥 Download Story Audio
                </a>
            `;
            
            document.getElementById('result').classList.add('show');
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('error').classList.add('show');
            document.getElementById('result').classList.remove('show');
        }

        // Add some helpful tips
        console.log('🎧 Nicole TTS Tips:');
        console.log('• Keep stories under 2000 characters for best results');
        console.log('• Nicole works best with narrative text and stories');
        console.log('• Audio generation takes 30-60 seconds');
        console.log('• Try the sample stories for quick testing');
    </script>
</body>
</html>
