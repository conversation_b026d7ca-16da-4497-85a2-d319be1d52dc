#!/usr/bin/env python3
"""
Kokoro TTS Web Application
موقع كامل لتحويل النص إلى صوت باستخدام Kokoro مع واجهة ويب جميلة
"""

from flask import Flask, request, jsonify, send_file, render_template_string
from kokoro import KPipeline
import soundfile as sf
import tempfile
import os
import torch
import logging
from pathlib import Path
import uuid
import time
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'kokoro-tts-secret-key'

# Initialize Kokoro pipeline
try:
    # Try to import and initialize Kokoro
    from kokoro import KPipeline
    pipeline = KPipeline(lang_code='a')
    logger.info("Kokoro pipeline initialized successfully")
except ImportError:
    logger.warning("Kokoro not installed. Running in demo mode.")
    pipeline = None
except Exception as e:
    logger.error(f"Failed to initialize Kokoro: {e}")
    pipeline = None

# Available voices with detailed info
VOICES = {
    'af_nicole': {'name': '🇺🇸 🚺 Nicole 🎧', 'lang': 'en-US', 'gender': 'female', 'quality': 'B', 'recommended': True},
    'af_heart': {'name': '🇺🇸 🚺 Heart ❤️', 'lang': 'en-US', 'gender': 'female', 'quality': 'A', 'recommended': True},
    'af_bella': {'name': '🇺🇸 🚺 Bella 🔥', 'lang': 'en-US', 'gender': 'female', 'quality': 'A', 'recommended': True},
    'af_sarah': {'name': '🇺🇸 🚺 Sarah', 'lang': 'en-US', 'gender': 'female', 'quality': 'B', 'recommended': False},
    'af_sky': {'name': '🇺🇸 🚺 Sky', 'lang': 'en-US', 'gender': 'female', 'quality': 'B', 'recommended': False},
    'am_michael': {'name': '🇺🇸 🚹 Michael', 'lang': 'en-US', 'gender': 'male', 'quality': 'A', 'recommended': True},
    'bf_emma': {'name': '🇬🇧 🚺 Emma', 'lang': 'en-GB', 'gender': 'female', 'quality': 'A', 'recommended': True},
    'bm_george': {'name': '🇬🇧 🚹 George', 'lang': 'en-GB', 'gender': 'male', 'quality': 'A', 'recommended': True}
}

# Statistics tracking
stats = {
    'total_requests': 0,
    'total_characters': 0,
    'total_audio_duration': 0,
    'voice_usage': {voice: 0 for voice in VOICES.keys()},
    'start_time': datetime.now()
}

@app.route('/')
def index():
    """Main web interface"""
    with open('templates/index.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'kokoro_available': pipeline is not None,
        'cuda_available': torch.cuda.is_available(),
        'total_requests': stats['total_requests']
    })

@app.route('/stats', methods=['GET'])
def get_stats():
    """Get server statistics"""
    uptime = datetime.now() - stats['start_time']
    return jsonify({
        'total_requests': stats['total_requests'],
        'total_characters': stats['total_characters'],
        'total_audio_duration': round(stats['total_audio_duration'], 2),
        'voice_usage': stats['voice_usage'],
        'uptime_hours': round(uptime.total_seconds() / 3600, 1),
        'uptime_days': uptime.days
    })

@app.route('/voices', methods=['GET'])
def get_voices():
    """Get available voices"""
    return jsonify({
        'voices': VOICES,
        'default': 'af_nicole'
    })

@app.route('/api/predict', methods=['POST'])
def api_predict():
    """API endpoint compatible with Gradio format"""
    if not pipeline:
        # Demo mode - return a sample audio URL
        logger.info("Running in demo mode - returning sample audio")
        return jsonify({
            'data': [{'url': 'https://www2.cs.uic.edu/~i101/SoundFiles/BabyElephantWalk60.wav'}],
            'success': True,
            'duration': 5.0,
            'voice': 'demo_mode',
            'text_length': len(request.get_json().get('data', [''])[0]),
            'note': 'Demo mode - install Kokoro for real TTS'
        })

    try:
        data = request.get_json()
        if not data or 'data' not in data:
            return jsonify({'error': 'Invalid request format'}), 400

        # Extract parameters from Gradio-style format
        params = data['data']
        if len(params) < 3:
            return jsonify({'error': 'Missing parameters'}), 400

        text = params[0]
        voice = params[1] if len(params) > 1 else 'af_nicole'
        speed = params[2] if len(params) > 2 else 1.0

        if not text:
            return jsonify({'error': 'No text provided'}), 400

        if voice not in VOICES:
            return jsonify({'error': f'Voice {voice} not available'}), 400

        # Update statistics
        stats['total_requests'] += 1
        stats['total_characters'] += len(text)
        stats['voice_usage'][voice] += 1

        logger.info(f"API Request: {len(text)} chars, voice: {voice}, speed: {speed}")

        # Generate audio
        generator = pipeline(text, voice=voice, speed=speed)

        # Collect all audio segments
        audio_segments = []
        for i, (gs, ps, audio) in enumerate(generator):
            audio_segments.append(audio)

        if not audio_segments:
            return jsonify({'error': 'No audio generated'}), 500

        # Concatenate audio segments
        if len(audio_segments) == 1:
            final_audio = audio_segments[0]
        else:
            final_audio = torch.cat(audio_segments, dim=0)

        # Calculate duration and update stats
        duration = len(final_audio) / 24000
        stats['total_audio_duration'] += duration

        # Save to static directory with unique filename
        static_dir = Path('static')
        static_dir.mkdir(exist_ok=True)

        filename = f'kokoro_{uuid.uuid4().hex[:8]}_{voice}.wav'
        file_path = static_dir / filename

        sf.write(str(file_path), final_audio.numpy(), 24000)

        # Return Gradio-compatible response
        base_url = request.url_root.rstrip('/')
        audio_url = f"{base_url}/static/{filename}"

        return jsonify({
            'data': [{'url': audio_url}],
            'success': True,
            'duration': duration,
            'voice': voice,
            'text_length': len(text)
        })

    except Exception as e:
        logger.error(f"API prediction failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/tts', methods=['POST'])
def text_to_speech():
    """Convert text to speech"""
    if not pipeline:
        return jsonify({'error': 'Kokoro pipeline not available'}), 500
    
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        text = data.get('text', '')
        voice = data.get('voice', 'af_nicole')
        speed = data.get('speed', 1.0)
        
        if not text:
            return jsonify({'error': 'No text provided'}), 400
        
        if voice not in VOICES:
            return jsonify({'error': f'Voice {voice} not available'}), 400
        
        logger.info(f"Generating TTS for text length: {len(text)}, voice: {voice}")
        
        # Generate audio
        generator = pipeline(text, voice=voice, speed=speed)
        
        # Collect all audio segments
        audio_segments = []
        for i, (gs, ps, audio) in enumerate(generator):
            audio_segments.append(audio)
            logger.info(f"Generated segment {i}: {len(audio)} samples")
        
        if not audio_segments:
            return jsonify({'error': 'No audio generated'}), 500
        
        # Concatenate audio segments
        if len(audio_segments) == 1:
            final_audio = audio_segments[0]
        else:
            final_audio = torch.cat(audio_segments, dim=0)
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
            sf.write(tmp_file.name, final_audio.numpy(), 24000)
            temp_path = tmp_file.name
        
        logger.info(f"Audio saved to: {temp_path}")
        
        # Return file
        return send_file(
            temp_path,
            as_attachment=True,
            download_name=f'kokoro_{voice}.wav',
            mimetype='audio/wav'
        )
        
    except Exception as e:
        logger.error(f"TTS generation failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/tts_url', methods=['POST'])
def text_to_speech_url():
    """Convert text to speech and return URL (for n8n compatibility)"""
    if not pipeline:
        return jsonify({'error': 'Kokoro pipeline not available'}), 500
    
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        text = data.get('text', '')
        voice = data.get('voice', 'af_nicole')
        speed = data.get('speed', 1.0)
        
        if not text:
            return jsonify({'error': 'No text provided'}), 400
        
        if voice not in VOICES:
            return jsonify({'error': f'Voice {voice} not available'}), 400
        
        logger.info(f"Generating TTS for text length: {len(text)}, voice: {voice}")
        
        # Generate audio
        generator = pipeline(text, voice=voice, speed=speed)
        
        # Collect all audio segments
        audio_segments = []
        for i, (gs, ps, audio) in enumerate(generator):
            audio_segments.append(audio)
        
        if not audio_segments:
            return jsonify({'error': 'No audio generated'}), 500
        
        # Concatenate audio segments
        if len(audio_segments) == 1:
            final_audio = audio_segments[0]
        else:
            final_audio = torch.cat(audio_segments, dim=0)
        
        # Save to static directory
        static_dir = Path('static')
        static_dir.mkdir(exist_ok=True)
        
        filename = f'kokoro_{hash(text)}_{voice}.wav'
        file_path = static_dir / filename
        
        sf.write(str(file_path), final_audio.numpy(), 24000)
        
        # Return URL
        base_url = request.url_root.rstrip('/')
        audio_url = f"{base_url}/static/{filename}"
        
        return jsonify({
            'success': True,
            'audio_url': audio_url,
            'voice': voice,
            'text_length': len(text),
            'duration': len(final_audio) / 24000
        })
        
    except Exception as e:
        logger.error(f"TTS generation failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/static/<filename>')
def serve_static(filename):
    """Serve static audio files"""
    static_dir = Path('static')
    file_path = static_dir / filename
    
    if file_path.exists():
        return send_file(str(file_path), mimetype='audio/wav')
    else:
        return jsonify({'error': 'File not found'}), 404

if __name__ == '__main__':
    # Create static directory
    Path('static').mkdir(exist_ok=True)
    
    # Start server
    app.run(host='0.0.0.0', port=5000, debug=False)
