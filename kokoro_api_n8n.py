#!/usr/bin/env python3
"""
Kokoro TTS API for n8n Integration
API مجاني ومفتوح المصدر لـ Kokoro TTS للاستخدام مع n8n
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import tempfile
import os
import logging
from datetime import datetime
import time
import uuid
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for n8n
app.secret_key = 'kokoro-n8n-api-key'

# Try to import Kokoro
try:
    from kokoro import KPipeline
    pipeline = KPipeline(lang_code='a')  # American English
    KOKORO_AVAILABLE = True
    logger.info("✅ Kokoro pipeline initialized successfully")
except ImportError:
    logger.warning("⚠️ Kokoro not installed. Install with: pip install kokoro")
    pipeline = None
    KOKORO_AVAILABLE = False
except Exception as e:
    logger.error(f"❌ Failed to initialize Kokoro: {e}")
    pipeline = None
    KOKORO_AVAILABLE = False

# Available voices for Kokoro
VOICES = {
    'af_nicole': {'name': 'Nicole (US Female)', 'lang': 'en-US', 'gender': 'female', 'recommended': True},
    'af_heart': {'name': 'Heart (US Female)', 'lang': 'en-US', 'gender': 'female', 'recommended': True},
    'af_bella': {'name': 'Bella (US Female)', 'lang': 'en-US', 'gender': 'female', 'recommended': True},
    'af_sarah': {'name': 'Sarah (US Female)', 'lang': 'en-US', 'gender': 'female', 'recommended': False},
    'af_sky': {'name': 'Sky (US Female)', 'lang': 'en-US', 'gender': 'female', 'recommended': False},
    'am_michael': {'name': 'Michael (US Male)', 'lang': 'en-US', 'gender': 'male', 'recommended': True},
    'bf_emma': {'name': 'Emma (UK Female)', 'lang': 'en-GB', 'gender': 'female', 'recommended': True},
    'bm_george': {'name': 'George (UK Male)', 'lang': 'en-GB', 'gender': 'male', 'recommended': True}
}

# Statistics
stats = {
    'total_requests': 0,
    'total_characters': 0,
    'total_audio_duration': 0,
    'voice_usage': {voice: 0 for voice in VOICES.keys()},
    'start_time': datetime.now()
}

@app.route('/', methods=['GET'])
def index():
    """API Information"""
    return jsonify({
        'name': 'Kokoro TTS API for n8n',
        'version': '1.0.0',
        'description': 'Free and open-source TTS API using Kokoro',
        'kokoro_available': KOKORO_AVAILABLE,
        'endpoints': {
            'health': '/health',
            'voices': '/voices',
            'tts': '/tts (POST)',
            'tts_url': '/tts_url (POST)',
            'stats': '/stats'
        },
        'usage': {
            'tts': 'POST /tts with {"text": "Hello", "voice": "af_nicole"}',
            'tts_url': 'POST /tts_url with {"text": "Hello", "voice": "af_nicole"}'
        }
    })

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for n8n monitoring"""
    return jsonify({
        'status': 'healthy',
        'kokoro_available': KOKORO_AVAILABLE,
        'total_requests': stats['total_requests'],
        'uptime_seconds': (datetime.now() - stats['start_time']).total_seconds()
    })

@app.route('/voices', methods=['GET'])
def get_voices():
    """Get available voices"""
    return jsonify({
        'success': True,
        'voices': VOICES,
        'default': 'af_nicole',
        'recommended': [voice for voice, info in VOICES.items() if info['recommended']]
    })

@app.route('/tts', methods=['POST'])
def text_to_speech():
    """
    Convert text to speech and return audio file
    Perfect for n8n workflows that need audio files
    """
    if not KOKORO_AVAILABLE:
        return jsonify({
            'success': False,
            'error': 'Kokoro not available. Install with: pip install kokoro',
            'demo_mode': True
        }), 500
    
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No JSON data provided'}), 400
        
        text = data.get('text', '').strip()
        voice = data.get('voice', 'af_nicole')
        speed = data.get('speed', 1.0)
        
        # Validation
        if not text:
            return jsonify({'success': False, 'error': 'No text provided'}), 400
        
        if voice not in VOICES:
            return jsonify({
                'success': False, 
                'error': f'Voice {voice} not available',
                'available_voices': list(VOICES.keys())
            }), 400
        
        if len(text) > 5000:
            return jsonify({'success': False, 'error': 'Text too long (max 5000 characters)'}), 400
        
        # Update statistics
        stats['total_requests'] += 1
        stats['total_characters'] += len(text)
        stats['voice_usage'][voice] += 1
        
        logger.info(f"🎵 TTS Request: {len(text)} chars, voice: {voice}")
        
        # Generate audio using Kokoro
        generator = pipeline(text, voice=voice, speed=speed)
        
        # Collect all audio segments
        audio_segments = []
        for i, (gs, ps, audio) in enumerate(generator):
            audio_segments.append(audio)
        
        if not audio_segments:
            return jsonify({'success': False, 'error': 'No audio generated'}), 500
        
        # Concatenate audio segments
        import torch
        if len(audio_segments) == 1:
            final_audio = audio_segments[0]
        else:
            final_audio = torch.cat(audio_segments, dim=0)
        
        # Calculate duration
        duration = len(final_audio) / 24000
        stats['total_audio_duration'] += duration
        
        # Save to temporary file
        import soundfile as sf
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
            sf.write(tmp_file.name, final_audio.numpy(), 24000)
            temp_path = tmp_file.name
        
        logger.info(f"✅ Audio generated: {duration:.2f}s, saved to: {temp_path}")
        
        # Return file for download
        return send_file(
            temp_path,
            as_attachment=True,
            download_name=f'kokoro_{voice}_{uuid.uuid4().hex[:8]}.wav',
            mimetype='audio/wav'
        )
        
    except Exception as e:
        logger.error(f"❌ TTS generation failed: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/tts_url', methods=['POST'])
def text_to_speech_url():
    """
    Convert text to speech and return URL
    Perfect for n8n workflows that need audio URLs
    """
    if not KOKORO_AVAILABLE:
        return jsonify({
            'success': False,
            'error': 'Kokoro not available. Install with: pip install kokoro',
            'demo_mode': True,
            'demo_audio_url': 'https://www2.cs.uic.edu/~i101/SoundFiles/BabyElephantWalk60.wav'
        }), 500
    
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No JSON data provided'}), 400
        
        text = data.get('text', '').strip()
        voice = data.get('voice', 'af_nicole')
        speed = data.get('speed', 1.0)
        
        # Validation
        if not text:
            return jsonify({'success': False, 'error': 'No text provided'}), 400
        
        if voice not in VOICES:
            return jsonify({
                'success': False, 
                'error': f'Voice {voice} not available',
                'available_voices': list(VOICES.keys())
            }), 400
        
        # Update statistics
        stats['total_requests'] += 1
        stats['total_characters'] += len(text)
        stats['voice_usage'][voice] += 1
        
        logger.info(f"🌐 TTS URL Request: {len(text)} chars, voice: {voice}")
        
        # Generate audio using Kokoro
        generator = pipeline(text, voice=voice, speed=speed)
        
        # Collect all audio segments
        audio_segments = []
        for i, (gs, ps, audio) in enumerate(generator):
            audio_segments.append(audio)
        
        if not audio_segments:
            return jsonify({'success': False, 'error': 'No audio generated'}), 500
        
        # Concatenate audio segments
        import torch
        if len(audio_segments) == 1:
            final_audio = audio_segments[0]
        else:
            final_audio = torch.cat(audio_segments, dim=0)
        
        # Calculate duration
        duration = len(final_audio) / 24000
        stats['total_audio_duration'] += duration
        
        # Save to static directory
        from pathlib import Path
        static_dir = Path('static')
        static_dir.mkdir(exist_ok=True)
        
        filename = f'kokoro_{hash(text)}_{voice}_{uuid.uuid4().hex[:8]}.wav'
        file_path = static_dir / filename
        
        import soundfile as sf
        sf.write(str(file_path), final_audio.numpy(), 24000)
        
        # Return URL
        base_url = request.url_root.rstrip('/')
        audio_url = f"{base_url}/static/{filename}"
        
        logger.info(f"✅ Audio URL generated: {audio_url}")
        
        return jsonify({
            'success': True,
            'audio_url': audio_url,
            'voice': voice,
            'text_length': len(text),
            'duration': duration,
            'voice_info': VOICES[voice]
        })
        
    except Exception as e:
        logger.error(f"❌ TTS URL generation failed: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/static/<filename>')
def serve_static(filename):
    """Serve static audio files"""
    from pathlib import Path
    static_dir = Path('static')
    file_path = static_dir / filename
    
    if file_path.exists():
        return send_file(str(file_path), mimetype='audio/wav')
    else:
        return jsonify({'success': False, 'error': 'File not found'}), 404

@app.route('/stats', methods=['GET'])
def get_stats():
    """Get API statistics"""
    uptime = datetime.now() - stats['start_time']
    return jsonify({
        'success': True,
        'total_requests': stats['total_requests'],
        'total_characters': stats['total_characters'],
        'total_audio_duration': round(stats['total_audio_duration'], 2),
        'voice_usage': stats['voice_usage'],
        'uptime_hours': round(uptime.total_seconds() / 3600, 1),
        'uptime_days': uptime.days,
        'kokoro_available': KOKORO_AVAILABLE
    })

if __name__ == '__main__':
    # Create static directory
    from pathlib import Path
    Path('static').mkdir(exist_ok=True)
    
    logger.info("🚀 Starting Kokoro TTS API for n8n...")
    logger.info("🌐 API Base URL: http://localhost:8000")
    logger.info("📡 Health Check: http://localhost:8000/health")
    logger.info("🎤 Voices: http://localhost:8000/voices")
    logger.info("🎵 TTS Endpoint: POST http://localhost:8000/tts")
    logger.info("🌐 TTS URL Endpoint: POST http://localhost:8000/tts_url")
    logger.info("📊 Stats: http://localhost:8000/stats")
    
    if KOKORO_AVAILABLE:
        logger.info("✅ Kokoro TTS is ready!")
    else:
        logger.info("⚠️ Running in demo mode. Install Kokoro: pip install kokoro")
    
    # Start server
    app.run(host='0.0.0.0', port=8000, debug=False)
