#!/usr/bin/env python3
"""
<PERSON> TTS Desktop App - واجهة سطح المكتب
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox
import requests
import threading
import os
from datetime import datetime
import webbrowser

class NicoleDesktopApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🎧 Nicole TTS - Desktop App")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # متغيرات
        self.is_generating = False
        self.current_audio_url = None
        
        self.setup_ui()
        
    def setup_ui(self):
        # العنوان
        title_frame = tk.Frame(self.root, bg='#667eea', height=80)
        title_frame.pack(fill='x', padx=10, pady=10)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame, 
            text="🎧 Nicole TTS Desktop App", 
            font=('Arial', 20, 'bold'),
            fg='white', 
            bg='#667eea'
        )
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(
            title_frame, 
            text="🇺🇸 🚺 American Female Voice for Bedtime Stories", 
            font=('Arial', 12),
            fg='white', 
            bg='#667eea'
        )
        subtitle_label.pack()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # مربع النص
        text_label = tk.Label(main_frame, text="📖 اكتب قصتك هنا:", font=('Arial', 12, 'bold'), bg='#f0f0f0')
        text_label.pack(anchor='w', pady=(0, 5))
        
        self.text_area = scrolledtext.ScrolledText(
            main_frame, 
            height=10, 
            font=('Arial', 11),
            wrap=tk.WORD
        )
        self.text_area.pack(fill='both', expand=True, pady=(0, 10))
        
        # النص التجريبي
        sample_text = """Once upon a time, in a magical forest far away, there lived a little rabbit named Luna. Every night before bed, Luna would look up at the twinkling stars and make a wish for sweet dreams and wonderful adventures. One evening, as Luna was getting ready for sleep, she noticed a gentle glow coming from behind the old oak tree."""
        self.text_area.insert('1.0', sample_text)
        
        # إطار الأزرار
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.pack(fill='x', pady=10)
        
        # زر إنتاج الصوت
        self.generate_btn = tk.Button(
            button_frame,
            text="🎵 إنتاج صوت Nicole",
            font=('Arial', 14, 'bold'),
            bg='#667eea',
            fg='white',
            command=self.generate_audio_thread,
            height=2
        )
        self.generate_btn.pack(side='left', fill='x', expand=True, padx=(0, 5))
        
        # زر تحميل الصوت
        self.download_btn = tk.Button(
            button_frame,
            text="📥 تحميل الصوت",
            font=('Arial', 12),
            bg='#28a745',
            fg='white',
            command=self.download_audio,
            state='disabled'
        )
        self.download_btn.pack(side='right', padx=(5, 0))

        # زر فتح HuggingFace
        self.hf_btn = tk.Button(
            button_frame,
            text="🌐 فتح HuggingFace",
            font=('Arial', 12),
            bg='#ffc107',
            fg='black',
            command=self.open_huggingface
        )
        self.hf_btn.pack(side='right', padx=(5, 0))
        
        # شريط التقدم
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill='x', pady=5)
        
        # منطقة الحالة
        self.status_label = tk.Label(
            main_frame, 
            text="🎤 Nicole جاهزة لقراءة قصتك", 
            font=('Arial', 10),
            bg='#f0f0f0',
            fg='#666'
        )
        self.status_label.pack(pady=5)
        
        # إطار النتائج
        result_frame = tk.LabelFrame(main_frame, text="🎵 النتائج", font=('Arial', 12, 'bold'), bg='#f0f0f0')
        result_frame.pack(fill='x', pady=10)
        
        self.result_text = tk.Text(result_frame, height=4, font=('Arial', 10), state='disabled')
        self.result_text.pack(fill='x', padx=10, pady=10)
        
    def update_status(self, message, color='#666'):
        self.status_label.config(text=message, fg=color)
        self.root.update()
        
    def update_result(self, text):
        self.result_text.config(state='normal')
        self.result_text.delete('1.0', tk.END)
        self.result_text.insert('1.0', text)
        self.result_text.config(state='disabled')

    def open_huggingface(self):
        """فتح HuggingFace Spaces في المتصفح"""
        urls = [
            'https://aiqcamp-mcp-kokoro.hf.space',
            'https://webml-community-kokoro-web.hf.space'
        ]

        for url in urls:
            webbrowser.open(url)

        self.update_status("🌐 تم فتح HuggingFace Spaces في المتصفح", '#28a745')

        # إظهار تعليمات
        instructions = """🌐 تم فتح HuggingFace Spaces في المتصفح

📋 تعليمات الاستخدام:
1. انسخ النص من هنا
2. الصقه في HuggingFace
3. اختر "af_nicole" للصوت
4. اضبط السرعة على 0.85
5. اضغط Submit

💡 إذا كان الموقع الأول مشغول، جرب الثاني."""

        self.update_result(instructions)
        
    def generate_audio_thread(self):
        if self.is_generating:
            return
            
        # تشغيل في thread منفصل لتجنب تجميد الواجهة
        thread = threading.Thread(target=self.generate_audio)
        thread.daemon = True
        thread.start()
        
    def generate_audio(self):
        try:
            self.is_generating = True
            self.generate_btn.config(state='disabled', text="🔄 جاري الإنتاج...")
            self.download_btn.config(state='disabled')
            self.progress.start()
            
            text = self.text_area.get('1.0', tk.END).strip()
            
            if not text:
                messagebox.showerror("خطأ", "يرجى إدخال نص للقصة!")
                return
                
            if len(text) > 2000:
                messagebox.showwarning("تحذير", "النص طويل جداً! يفضل أقل من 2000 حرف.")
                
            self.update_status("🎧 Nicole تقرأ قصتك... يرجى الانتظار", '#667eea')
            
            # المحاولة مع HuggingFace Spaces
            endpoints = [
                {
                    'url': 'https://aiqcamp-mcp-kokoro.hf.space/api/predict',
                    'name': 'MCP-Kokoro',
                    'payload_type': 'mcp'
                },
                {
                    'url': 'https://webml-community-kokoro-web.hf.space/api/predict',
                    'name': 'WebML-Kokoro',
                    'payload_type': 'webml'
                },
                {
                    'url': 'https://huggingface.co/spaces/aiqcamp/MCP-kokoro',
                    'name': 'Direct-MCP',
                    'payload_type': 'direct'
                }
            ]
            
            for i, endpoint in enumerate(endpoints, 1):
                try:
                    self.update_status(f"🔄 المحاولة {i}/{len(endpoints)}: {endpoint['name']}", '#ffc107')

                    # تحضير البيانات حسب نوع الخدمة
                    if endpoint['payload_type'] == 'mcp':
                        payload = {'data': [text, 'af_nicole', 0.85]}
                    elif endpoint['payload_type'] == 'webml':
                        payload = {'data': [text, 'af_nicole', 0.85, False]}
                    else:  # direct
                        # للروابط المباشرة، نفتحها في المتصفح
                        webbrowser.open(endpoint['url'])
                        self.update_status(f"🌐 تم فتح {endpoint['name']} في المتصفح", '#28a745')
                        continue

                    response = requests.post(
                        endpoint['url'],
                        json=payload,
                        headers={'Content-Type': 'application/json'},
                        timeout=120
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        
                        if result.get('data') and result['data'][0]:
                            audio_data = result['data'][0]
                            audio_url = audio_data.get('url') if isinstance(audio_data, dict) else audio_data
                            
                            if audio_url and audio_url.startswith('/'):
                                base_url = endpoint['url'].replace('/api/predict', '')
                                audio_url = base_url + audio_url

                            if audio_url:
                                self.current_audio_url = audio_url
                                self.update_status("✅ تم إنتاج الصوت بنجاح!", '#28a745')

                                result_info = f"""✅ تم إنتاج الصوت بنجاح!
🎤 الصوت: Nicole 🇺🇸 🚺 (American Female)
📊 طول النص: {len(text)} حرف
🏢 المزود: {endpoint['name']}
🌐 رابط الصوت: {audio_url}

💡 يمكنك الآن تحميل الصوت أو تشغيله في المتصفح."""
                                
                                self.update_result(result_info)
                                self.download_btn.config(state='normal')
                                
                                # فتح الصوت في المتصفح
                                webbrowser.open(audio_url)
                                return
                
                except Exception as e:
                    self.update_status(f"❌ فشل {endpoint['name']}: {str(e)[:50]}...", '#dc3545')
                    continue
            
            # إذا فشلت جميع المحاولات
            self.update_status("❌ جميع الخدمات غير متاحة حالياً", '#dc3545')
            self.update_result("❌ فشل في إنتاج الصوت\n\nجميع خدمات HuggingFace Spaces غير متاحة حالياً.\n\nنصائح:\n• جرب مرة أخرى بعد دقائق\n• تأكد من اتصال الإنترنت\n• جرب في وقت آخر من اليوم")
            
        except Exception as e:
            self.update_status(f"❌ خطأ: {str(e)}", '#dc3545')
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
            
        finally:
            self.is_generating = False
            self.generate_btn.config(state='normal', text="🎵 إنتاج صوت Nicole")
            self.progress.stop()
            
    def download_audio(self):
        if not self.current_audio_url:
            messagebox.showerror("خطأ", "لا يوجد صوت للتحميل!")
            return
            
        try:
            # اختيار مكان الحفظ
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"nicole_story_{timestamp}.wav"
            
            file_path = filedialog.asksaveasfilename(
                defaultextension=".wav",
                filetypes=[("Audio files", "*.wav"), ("All files", "*.*")],
                initialvalue=default_name
            )
            
            if file_path:
                self.update_status("📥 جاري تحميل الصوت...", '#ffc107')
                
                # تحميل الصوت
                audio_response = requests.get(self.current_audio_url, timeout=60)
                if audio_response.status_code == 200:
                    with open(file_path, 'wb') as f:
                        f.write(audio_response.content)
                    
                    self.update_status(f"✅ تم التحميل: {os.path.basename(file_path)}", '#28a745')
                    messagebox.showinfo("نجح التحميل", f"تم حفظ الصوت في:\n{file_path}")
                else:
                    raise Exception(f"فشل التحميل: {audio_response.status_code}")
                    
        except Exception as e:
            self.update_status(f"❌ فشل التحميل: {str(e)}", '#dc3545')
            messagebox.showerror("خطأ في التحميل", f"فشل في تحميل الصوت:\n{str(e)}")

def main():
    root = tk.Tk()
    app = NicoleDesktopApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
